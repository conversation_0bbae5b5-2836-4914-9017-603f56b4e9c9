#!/bin/bash

# 进入项目目录
cd /mnt/persist/workspace

# 安装TypeScript依赖
npm install --save-dev typescript

# 创建测试功能 - 在 utils 中添加新的工具函数
cat >> miniprogram/utils/util.ts << 'EOF'

// 新增的测试功能：格式化价格显示
export const formatPrice = (price: number): string => {
  if (typeof price !== 'number' || price < 0) {
    return '¥0.00';
  }
  return `¥${price.toFixed(2)}`;
};

// 新增的测试功能：计算折扣价格
export const calculateDiscountPrice = (originalPrice: number, discountPercent: number): number => {
  if (typeof originalPrice !== 'number' || typeof discountPercent !== 'number') {
    return originalPrice;
  }
  if (discountPercent < 0 || discountPercent > 100) {
    return originalPrice;
  }
  return originalPrice * (1 - discountPercent / 100);
};

// 新增的测试功能：验证手机号格式
export const validatePhoneNumber = (phone: string): boolean => {
  if (typeof phone !== 'string') {
    return false;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};
EOF

# 创建测试页面目录
mkdir -p miniprogram/pages/test

# 创建测试页面 TypeScript 文件
cat > miniprogram/pages/test/index.ts << 'EOF'
// test/index.ts
import { formatPrice, calculateDiscountPrice, validatePhoneNumber } from '../../utils/util';

interface TestResult {
  test: string;
  input: string;
  output: string;
  expected: string;
}

Component({
  data: {
    testResults: [] as TestResult[],
    phoneInput: '',
    phoneValidResult: ''
  },

  lifetimes: {
    attached() {
      this.runTests();
    }
  },

  methods: {
    runTests() {
      const results: TestResult[] = [];
      
      // 测试价格格式化功能
      results.push({
        test: '价格格式化测试',
        input: '68',
        output: formatPrice(68),
        expected: '¥68.00'
      });
      
      results.push({
        test: '价格格式化测试（小数）',
        input: '99.9',
        output: formatPrice(99.9),
        expected: '¥99.90'
      });
      
      // 测试折扣计算功能
      results.push({
        test: '折扣计算测试',
        input: '原价100，8折',
        output: calculateDiscountPrice(100, 20).toString(),
        expected: '80'
      });
      
      results.push({
        test: '折扣计算测试',
        input: '原价68，9折',
        output: calculateDiscountPrice(68, 10).toFixed(2),
        expected: '61.20'
      });
      
      // 测试手机号验证功能
      results.push({
        test: '手机号验证测试（有效）',
        input: '13812345678',
        output: validatePhoneNumber('13812345678').toString(),
        expected: 'true'
      });
      
      results.push({
        test: '手机号验证测试（无效）',
        input: '12345678901',
        output: validatePhoneNumber('12345678901').toString(),
        expected: 'false'
      });

      this.setData({
        testResults: results
      });
    },

    onPhoneInput(e: any) {
      const phone = e.detail.value;
      const isValid = validatePhoneNumber(phone);
      this.setData({
        phoneInput: phone,
        phoneValidResult: isValid ? '✅ 手机号格式正确' : '❌ 手机号格式错误'
      });
    },

    // 测试价格计算功能
    testPriceCalculation() {
      const originalPrice = 88;
      const discount = 15; // 15% 折扣
      const discountedPrice = calculateDiscountPrice(originalPrice, discount);
      const formattedPrice = formatPrice(discountedPrice);
      
      wx.showModal({
        title: '价格计算测试',
        content: `原价：${formatPrice(originalPrice)}\n折扣：${discount}%\n折后价：${formattedPrice}`,
        showCancel: false
      });
    }
  }
});
EOF

# 创建测试页面 WXML 文件
cat > miniprogram/pages/test/index.wxml << 'EOF'
<!--test/index.wxml-->
<view class="container">
  <view class="header">
    <text class="title">功能测试页面</text>
  </view>

  <!-- 自动测试结果 -->
  <view class="test-section">
    <text class="section-title">自动测试结果</text>
    <view class="test-item" wx:for="{{testResults}}" wx:key="test">
      <view class="test-name">{{item.test}}</view>
      <view class="test-detail">
        <text class="test-input">输入: {{item.input}}</text>
        <text class="test-output">输出: {{item.output}}</text>
        <text class="test-expected">期望: {{item.expected}}</text>
        <text class="test-result {{item.output === item.expected ? 'pass' : 'fail'}}">
          {{item.output === item.expected ? '✅ 通过' : '❌ 失败'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 手动测试区域 -->
  <view class="test-section">
    <text class="section-title">手动测试区域</text>
    
    <!-- 手机号验证测试 -->
    <view class="manual-test">
      <text class="test-label">手机号验证测试：</text>
      <input 
        class="phone-input" 
        placeholder="请输入手机号" 
        bindinput="onPhoneInput"
        value="{{phoneInput}}"
        type="number"
        maxlength="11"
      />
      <text class="validation-result">{{phoneValidResult}}</text>
    </view>

    <!-- 价格计算测试按钮 -->
    <button class="test-button" bindtap="testPriceCalculation">
      测试价格计算功能
    </button>
  </view>
</view>
EOF

# 创建测试页面 WXSS 文件
cat > miniprogram/pages/test/index.wxss << 'EOF'
/* test/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4D4CC3;
  margin-bottom: 20rpx;
  display: block;
}

.test-item {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.test-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.test-detail {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.test-input, .test-output, .test-expected {
  font-size: 24rpx;
  color: #666;
}

.test-result {
  font-size: 26rpx;
  font-weight: bold;
  margin-top: 10rpx;
}

.test-result.pass {
  color: #52c41a;
}

.test-result.fail {
  color: #ff4d4f;
}

.manual-test {
  margin-bottom: 30rpx;
}

.test-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.phone-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 15rpx;
  box-sizing: border-box;
}

.validation-result {
  font-size: 26rpx;
  font-weight: bold;
  display: block;
}

.test-button {
  width: 100%;
  height: 80rpx;
  background-color: #4D4CC3;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.test-button:active {
  background-color: #3a3a9a;
}
EOF

# 创建测试页面 JSON 配置文件
cat > miniprogram/pages/test/index.json << 'EOF'
{
  "navigationBarTitleText": "功能测试"
}
EOF

# 将测试页面添加到 app.json 中
node -e "
const fs = require('fs');
const appConfig = JSON.parse(fs.readFileSync('miniprogram/app.json', 'utf8'));
if (!appConfig.pages.includes('pages/test/index')) {
  appConfig.pages.push('pages/test/index');
}
fs.writeFileSync('miniprogram/app.json', JSON.stringify(appConfig, null, 2));
console.log('已将测试页面添加到 app.json');
"

# 编译检查语法
./node_modules/.bin/tsc --noEmit --skipLibCheck

# 显示项目结构
echo ""
echo "📁 项目结构："
ls -la miniprogram/pages/test/

echo ""
echo "📄 检查 app.json 页面配置："
node -e "
const fs = require('fs');
const appConfig = JSON.parse(fs.readFileSync('miniprogram/app.json', 'utf8'));
console.log('页面列表:');
appConfig.pages.forEach((page, index) => {
  console.log(\`\${index + 1}. \${page}\`);
});
"

echo ""
echo "✅ 开发环境设置完成！"
echo "✅ 已创建测试功能和测试页面"
echo ""
echo "📋 手动测试步骤："
echo "1. 使用微信开发者工具打开项目目录：/mnt/persist/workspace"
echo "2. 在模拟器中导航到 '功能测试' 页面（页面列表中的第6个）"
echo "3. 查看自动测试结果"
echo "4. 在手动测试区域输入手机号测试验证功能"
echo "5. 点击按钮测试价格计算功能"
echo ""
echo "🔧 新增的工具函数："
echo "- formatPrice(): 格式化价格显示"
echo "- calculateDiscountPrice(): 计算折扣价格"
echo "- validatePhoneNumber(): 验证手机号格式"
echo ""
echo "📱 测试功能说明："
echo "- 价格格式化：将数字转换为货币格式（¥68.00）"
echo "- 折扣计算：根据原价和折扣百分比计算折后价"
echo "- 手机号验证：验证中国大陆手机号格式是否正确"