import api from '../../../utils/http';
import drawQrcode from '../../../utils/weapp.qrcode.min.js';

Page({
  data: {
    orderId: '',
    checkin: null,
    detail: null,
    loading: true
  },

  onLoad(options) {
    if (options.orderId) {
      const params = { orderId: options.orderId };
      if (options.checkin) params.checkin = options.checkin;
      this.setData(params);
      this.loadOrderDetail();
    } else {
      wx.showToast({ title: '订单ID不能为空', icon: 'none' });
      setTimeout(() => {
        wx.navigateBack();
      }, 3000);
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });
      const detail = await api.getOrderDetail(this.data.orderId);
      this.setData({ detail: detail, loading: false });
      drawQrcode({
        width: this.rpxToPx(240),
        height: this.rpxToPx(240),
        canvasId: 'ticketQrcode',
        text: detail.vcode || '',
        foreground: this.data.checkin || detail.status === '待使用' ? '#000000' : '#EEE'
      });
    } catch (error) {
      wx.showToast({ title: '订单详情加载失败', icon: 'none' });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  rpxToPx(rpx) {
    const systemInfo = wx.getWindowInfo();
    console.log(systemInfo);
    return (systemInfo.windowWidth / 750) * rpx;
  },

  onPullDownRefresh() {
    this.loadOrderDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});
