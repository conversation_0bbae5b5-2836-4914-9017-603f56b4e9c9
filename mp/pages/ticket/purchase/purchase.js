import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    navBarBottom: 0,
    id: 0,
    title: '',
    prices: {},
    timeslots: [], // 场次列表
    catg: null,
    selectedTimeslot: null, // 当前选择的场次
    selectedGrade: null, // 当前选择的票档
    selectedPrice: 0, // 当前选择票档的单价
    quantity: 1, // 购买数量
    amount: 0, // 总价
    buttonText: '请选择场次', // 按钮文字
    buttonDisabled: true, // 按钮是否禁用
    openid: null,
    phone: null,
    showPhoneDialog: false,
    inputPhone: '',
    isValidPhone: false
  },

  onLoad(options) {
    // 获取导航栏高度
    this.setData({ navBarBottom: wx.getMenuButtonBoundingClientRect().bottom });

    // 根据传入的type获取对应的数据
    if (options.type === 'ticket') {
      api.getTicketPrice(options.id).then((res) => {
        this.setData({ id: options.id, title: res.title, prices: res.prices, catg: res.catg });
        this.setData({ timeslots: Object.keys(this.data.prices) }); // 初始化场次列表
      });
    } else if (options.type == 'reserve') {
      api.getTicketPrice(options.id).then((res) => {
        this.setData({ id: options.id, title: res.title, prices: res.prices, catg: res.catg });
        this.setData({ timeslots: Object.keys(this.data.prices) }); // 初始化场次列表
      });
    } else if (options.type === 'joint') {
      api.getJointPrice(options.id).then((res) => {
        this.setData({ id: options.id, title: res.title, prices: res.prices, catg: res.catg });
        this.setData({ timeslots: Object.keys(this.data.prices) }); // 初始化场次列表
      });
    } else {
      wx.showToast({ title: '门票类型无效', icon: 'none' });
    }
  },

  onShow() {
    // 检查登录状态
    const user = Cache.get('user');
    this.setData({ openid: user.openid, phone: user.phone });
  },

  // 选择场次
  selectTimeslot(e) {
    const timeslot = e.currentTarget.dataset.timeslot;
    if (this.data.selectedTimeslot === timeslot) {
      // 如果重复点击已选中的场次，则取消选择
      this.setData({
        selectedTimeslot: null,
        selectedGrade: null,
        selectedPrice: 0,
        quantity: 1,
        amount: 0,
        buttonText: '请选择场次',
        buttonDisabled: true
      });
    } else {
      this.setData({
        selectedTimeslot: timeslot,
        selectedGrade: null, // 重置票档选择
        selectedPrice: 0,
        quantity: 1,
        amount: 0,
        buttonText: '请选择票档',
        buttonDisabled: true
      });
    }
  },

  // 选择票档
  selectGrade(e) {
    const grade = e.currentTarget.dataset.grade;
    const priceArray = this.data.prices[this.data.selectedTimeslot][grade];
    const currentGrade = priceArray[0];
    const currentPrice = priceArray[1];

    if (this.data.selectedGrade === currentGrade) {
      // 如果重复点击已选中的票档，则取消选择
      this.setData({
        selectedGrade: null,
        selectedPrice: 0,
        quantity: 1,
        amount: 0,
        buttonText: '请选择票档',
        buttonDisabled: true
      });
    } else {
      this.setData({
        selectedGrade: currentGrade,
        selectedPrice: currentPrice,
        quantity: 1, // 默认数量为1
        amount: currentPrice * 1,
        buttonText: '提交订单',
        buttonDisabled: false
      });
    }
  },

  // 数量减
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      const newQuantity = this.data.quantity - 1;
      this.setData({
        quantity: newQuantity,
        amount: this.data.selectedPrice * newQuantity
      });
    }
  },

  // 数量加
  increaseQuantity() {
    // 这里可以根据实际需求设置每单限购数量，例如 10
    if (this.data.quantity < 10) {
      const newQuantity = this.data.quantity + 1;
      this.setData({
        quantity: newQuantity,
        amount: this.data.selectedPrice * newQuantity
      });
    } else {
      wx.showToast({ title: '每单限购 10 份', icon: 'none' });
    }
  },

  // 获取手机号
  async getPhoneAndPurchase(e) {
    console.log(e);
    if (!this.data.openid) {
      await api.login();
    }
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      api.getUserPhone(res.detail.encryptedData, res.detail.iv, res.detail.code).then((phone) => {
        this.setData({ phone });
        this.handlePurchase();
      });
    } else {
      // 显示手机号输入对话框
      this.setData({
        showPhoneDialog: true,
        inputPhone: '',
        isValidPhone: false
      });
      // this.handlePurchase();
    }
  },

  // 处理购买
  handlePurchase() {
    if (!this.data.buttonDisabled) {
      // 执行购买逻辑，例如跳转到支付页面或显示确认信息
      api
        .submitOrder(
          this.data.id,
          this.data.catg,
          this.data.quantity,
          this.data.amount,
          this.data.selectedTimeslot,
          this.data.selectedGrade
        )
        .then((res) => {
          wx.requestPayment({
            timeStamp: res.timeStamp,
            nonceStr: res.nonceStr,
            package: res.package,
            signType: res.signType,
            paySign: res.paySign,
            success: function (res) {
              wx.shotToast({ title: '支付成功(模拟)', icon: 'success' });
            },
            fail: function (res) {
              wx.shotToast({ title: '支付失败(模拟)', icon: 'error' });
            },
            complete: function (res) {}
          });
        });

      // 如果是预约订单，跳转到预约页面
      if (this.data.catg === '预约票') {
        wx.navigateTo({ url: `/pages/reservation/reserve/reserve?id=${this.data.id}` });
      }
    }
  },

  // 手机号输入处理
  onPhoneInput(e) {
    const phone = e.detail.value;
    // 验证手机号是否为11位数字
    const isValid = /^1\d{10}$/.test(phone);
    this.setData({
      inputPhone: phone,
      isValidPhone: isValid
    });
  },

  // 关闭手机号输入对话框
  closePhoneDialog() {
    this.setData({
      showPhoneDialog: false,
      inputPhone: '',
      isValidPhone: false
    });
  },

  // 提交手动输入的手机号
  submitPhone() {
    if (this.data.isValidPhone) {
      const phone = this.data.inputPhone;
      this.setData({ phone: phone, showPhoneDialog: false });
      api.bindUserPhone(phone);
    }
  }
});
