import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    nickname: null, // 用于展示的昵称
    name: null,
    avatar: null,
    phone: null,
    showPhoneDialog: false,
    inputPhone: '',
    isValidPhone: false
  },

  onShow: function () {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 4 });
    }
    const user = Cache.get('user');
    if (!this.data.avatar || !this.data.phone) {
      this.setData({ name: user.name, avatar: user.avatar, phone: user.phone });
    }

    this.setNickname();
  },

  setNickname: function () {
    if (this.data.name && this.data.phone) {
      this.setData({ nickname: `📱 ${this.data.name}` });
    } else if (this.data.name) {
      this.setData({ nickname: this.data.name });
    } else if (this.data.phone) {
      this.setData({ nickname: `手机用户 ${this.data.phone.slice(7)}` });
    } else {
      this.setData({ nickname: '使用微信头像' });
    }
  },

  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        api.getProfile(res.encryptedData, res.iv).then((profile) => {
          this.setData({ name: profile.name, avatar: profile.avatar });
          this.setNickname();
        });
      }
    });
  },

  goToUserOrders: function () {
    wx.navigateTo({
      url: '/pages/order/index/index'
    });
  },

  goToThirdpartyOrders: function () {
    wx.showToast({
      title: '即将开放，敬请期待！',
      icon: 'none'
    });
  },
  // 导航到用户须知页面
  goToUserNotice: function () {
    wx.navigateTo({
      url: '/pages/user/notice/notice'
    }).catch((res) => {
      console.log(res);
    });
  },

  goToUserPrivacy: function () {
    wx.navigateTo({
      url: '/pages/user/privacy/privacy'
    });
  },

  // 获取手机号
  getPhoneNumber: function (res) {
    if (res.detail.errMsg === 'getPhoneNumber:ok') {
      api.getUserPhone(res.detail.encryptedData, res.detail.iv, res.detail.code).then((phone) => {
        this.setData({ phone });
        this.setNickname();
      });
    } else {
      // 显示手机号输入对话框
      this.setData({
        showPhoneDialog: true,
        inputPhone: '',
        isValidPhone: false
      });
    }
  },

  // 手机号输入处理
  onPhoneInput(e) {
    const phone = e.detail.value;
    // 验证手机号是否为11位数字
    const isValid = /^1\d{10}$/.test(phone);
    this.setData({
      inputPhone: phone,
      isValidPhone: isValid
    });
  },

  // 关闭手机号输入对话框
  closePhoneDialog() {
    this.setData({
      showPhoneDialog: false,
      inputPhone: '',
      isValidPhone: false
    });
  },

  // 提交手动输入的手机号
  submitPhone() {
    if (this.data.isValidPhone) {
      const phone = this.data.inputPhone;
      this.setData({ phone: phone, showPhoneDialog: false });
      api.bindUserPhone(phone).then(() => {
        this.setNickname();
      });
    }
  },

  userLogout() {
    Cache.del('user');
    this.setData({ name: null, avatar: null, phone: null });
    this.setNickname();
  }
});
