from fastapi import Request
from fastapi.responses import RedirectResponse
from starlette.authentication import AuthCredentials, UnauthenticatedUser
from tortoise.exceptions import DoesNotExist

from admin.models import Admin
from libs.middleware import BaseMiddleware


class AuthenticationMiddleware(BaseMiddleware):
    """认证中间件"""

    def need_auth(self, path: str) -> bool:
        """判断是否需要认证"""
        return path.startswith('/adm') and not path.startswith('/adm/login')

    async def authenticate(self, request: Request):
        """认证用户"""
        if uid := request.session.get('uid'):
            try:
                manager = await Admin.get(id=uid)
                request.scope['auth'] = AuthCredentials(['authenticated'])
                request.scope['user'] = manager
                return True
            except (DoesNotExist, Exception):  # noqa: S110
                pass
        request.scope['auth'] = AuthCredentials()
        request.scope['user'] = UnauthenticatedUser()
        return False

    async def process_request(self, request: Request):
        """处理请求: 检查用户登录状态"""
        if not self.need_auth(request.url.path):
            request.scope['auth'] = AuthCredentials()
            request.scope['user'] = UnauthenticatedUser()
            return

        if not await self.authenticate(request):
            return RedirectResponse('/adm/login/')
