from enum import Str<PERSON>num
from typing import Any, ClassVar

from starlette.authentication import BaseUser
from tortoise import fields

from config import SECRET_KEY
from libs.attrdict import AttrDict
from libs.orm import Model
from libs.utils import make_signature, verify_signature


class Admin(Model, BaseUser):
    """管理员用户表"""

    username = fields.CharField(max_length=32, unique=True, description='用户名')
    password = fields.CharField(max_length=128, null=False, description='密码')
    phone = fields.CharField(max_length=11, null=True, description='手机号')
    intro = fields.CharField(max_length=128, null=True, description='简介')
    created = fields.DatetimeField(auto_now_add=True, description='创建时间')

    class Meta:  # type: ignore
        table = 'admin_user'
        ordering: ClassVar[list[str]] = ['id']

    @property
    def is_authenticated(self) -> bool:
        return True

    @property
    def display_name(self) -> str:
        return self.username

    def verify_password(self, plain_password):
        """验证密码"""
        return verify_signature(plain_password, self.password, secret_key=SECRET_KEY)

    @staticmethod
    def hash_password(plain_password):
        return make_signature(plain_password, secret_key=SECRET_KEY)


class Setting(Model):
    """系统设置表"""

    class Type(StrEnum):
        INT = 'int'
        FLOAT = 'float'
        BOOL = 'bool'
        STR = 'str'
        TEXT = 'text'
        JSON = 'json'

    name = fields.CharField(max_length=32, unique=True, description='配置项名称')
    vtype = fields.CharEnumField(Type, max_length=8, null=False, description='配置项类型')
    vint = fields.IntField(null=True, description='整型值')
    vfloat = fields.FloatField(null=True, description='浮点型值')
    vstr = fields.CharField(max_length=256, null=True, description='字符串值')
    vtext = fields.TextField(null=True, description='文本值')
    vjson: Any = fields.JSONField(null=True, description='JSON值')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    @property
    def value(self):
        match self.vtype:
            case self.Type.INT:
                return self.vint
            case self.Type.FLOAT:
                return self.vfloat
            case self.Type.BOOL:
                return bool(self.vint)
            case self.Type.STR:
                return self.vstr
            case self.Type.TEXT:
                return self.vtext
            case self.Type.JSON:
                return self.vjson

        raise TypeError(f'Invalid setting type: {self.vtype}')

    @classmethod
    async def value_of(cls, name):
        if obj := await cls.get_or_none(name=name):
            return obj.value
        return None

    @classmethod
    async def values(cls, *keys) -> AttrDict:
        if keys:
            return AttrDict({o.name: o.value for o in await cls.filter(name__in=keys)})
        else:
            return AttrDict({o.name: o.value for o in await cls.all()})
