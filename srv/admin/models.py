from enum import StrEnum
from typing import Any, ClassVar

from starlette.authentication import BaseUser
from tortoise import fields

from config import SECRET_KEY
from libs.attrdict import AttrDict
from libs.orm import Model, relate_cache
from libs.utils import make_signature, verify_signature


class AdminRole(StrEnum):
    """管理员角色枚举"""

    SUPER_ADMIN = 'super_admin'  # 超级管理员
    ADMIN = 'admin'  # 普通管理员
    STORE_MANAGER = 'store_manager'  # 店长
    STAFF = 'staff'  # 员工
    OPERATOR = 'operator'  # 运营账号


class Admin(Model, BaseUser):
    """管理员用户表"""

    username = fields.CharField(max_length=32, unique=True, description='用户名')
    password = fields.CharField(max_length=128, null=False, description='密码')
    phone = fields.CharField(max_length=11, null=True, description='手机号')
    intro = fields.Char<PERSON>ield(max_length=128, null=True, description='简介')

    # RBAC 相关字段
    role = fields.CharEnumField(AdminRole, max_length=32, default=AdminRole.STAFF, description='角色')
    store_id = fields.IntField(null=True, description='关联门店ID（店长和员工必填）')
    is_active = fields.BooleanField(default=True, description='是否激活')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    class Meta:  # type: ignore
        table = 'admin_user'
        ordering: ClassVar[list[str]] = ['id']

    @property
    def is_authenticated(self) -> bool:
        return True

    @property
    def display_name(self) -> str:
        return self.username

    def verify_password(self, plain_password):
        """验证密码"""
        return verify_signature(plain_password, self.password, secret_key=SECRET_KEY)

    @staticmethod
    def hash_password(plain_password):
        return make_signature(plain_password, secret_key=SECRET_KEY)

    @relate_cache
    async def load_store(self):
        """加载关联的门店信息"""
        if self.store_id:
            from apps.exhi.models import ExhiHall

            return await ExhiHall.get_or_none(id=self.store_id)
        return None

    def can_manage_store(self, store_id: int) -> bool:
        """检查是否可以管理指定门店"""
        if self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.OPERATOR]:
            return True
        return self.store_id == store_id

    def can_create_role(self, target_role: AdminRole) -> bool:
        """检查是否可以创建指定角色的账号"""
        role_hierarchy = {
            AdminRole.SUPER_ADMIN: [
                AdminRole.SUPER_ADMIN,
                AdminRole.ADMIN,
                AdminRole.STORE_MANAGER,
                AdminRole.STAFF,
                AdminRole.OPERATOR,
            ],
            AdminRole.ADMIN: [AdminRole.STORE_MANAGER, AdminRole.STAFF, AdminRole.OPERATOR],
            AdminRole.STORE_MANAGER: [AdminRole.STAFF],
            AdminRole.STAFF: [],
            AdminRole.OPERATOR: [],
        }
        return target_role in role_hierarchy.get(self.role, [])

    # 简化的权限检查方法
    def can_view_users(self) -> bool:
        """是否可以查看用户"""
        return self.role in [
            AdminRole.SUPER_ADMIN,
            AdminRole.ADMIN,
            AdminRole.STORE_MANAGER,
            AdminRole.STAFF,
            AdminRole.OPERATOR,
        ]

    def can_create_users(self) -> bool:
        """是否可以创建用户"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER]

    def can_edit_users(self) -> bool:
        """是否可以编辑用户"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER]

    def can_delete_users(self) -> bool:
        """是否可以删除用户"""
        return self.role == AdminRole.SUPER_ADMIN

    def can_view_orders(self) -> bool:
        """是否可以查看订单"""
        return self.role in [
            AdminRole.SUPER_ADMIN,
            AdminRole.ADMIN,
            AdminRole.STORE_MANAGER,
            AdminRole.STAFF,
            AdminRole.OPERATOR,
        ]

    def can_create_orders(self) -> bool:
        """是否可以创建订单"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER]

    def can_edit_orders(self) -> bool:
        """是否可以编辑订单"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER]

    def can_delete_orders(self) -> bool:
        """是否可以删除订单"""
        return self.role == AdminRole.SUPER_ADMIN

    def can_checkin_tickets(self) -> bool:
        """是否可以验票"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER, AdminRole.STAFF]

    def can_manage_exhibitions(self) -> bool:
        """是否可以管理展览"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]

    def can_manage_halls(self) -> bool:
        """是否可以管理展馆"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]

    def can_manage_tickets(self) -> bool:
        """是否可以管理门票"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]

    def can_manage_admins(self) -> bool:
        """是否可以管理管理员"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER]

    def can_manage_settings(self) -> bool:
        """是否可以管理系统设置"""
        return self.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]


class Setting(Model):
    """系统设置表"""

    class Type(StrEnum):
        INT = 'int'
        FLOAT = 'float'
        BOOL = 'bool'
        STR = 'str'
        TEXT = 'text'
        JSON = 'json'

    name = fields.CharField(max_length=32, unique=True, description='配置项名称')
    vtype = fields.CharEnumField(Type, max_length=8, null=False, description='配置项类型')
    vint = fields.IntField(null=True, description='整型值')
    vfloat = fields.FloatField(null=True, description='浮点型值')
    vstr = fields.CharField(max_length=256, null=True, description='字符串值')
    vtext = fields.TextField(null=True, description='文本值')
    vjson: Any = fields.JSONField(null=True, description='JSON值')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    @property
    def value(self):
        match self.vtype:
            case self.Type.INT:
                return self.vint
            case self.Type.FLOAT:
                return self.vfloat
            case self.Type.BOOL:
                return bool(self.vint)
            case self.Type.STR:
                return self.vstr
            case self.Type.TEXT:
                return self.vtext
            case self.Type.JSON:
                return self.vjson

        raise TypeError(f'Invalid setting type: {self.vtype}')

    @classmethod
    async def value_of(cls, name):
        if obj := await cls.get_or_none(name=name):
            return obj.value
        return None

    @classmethod
    async def values(cls, *keys) -> AttrDict:
        if keys:
            return AttrDict({o.name: o.value for o in await cls.filter(name__in=keys)})
        else:
            return AttrDict({o.name: o.value for o in await cls.all()})
