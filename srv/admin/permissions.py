"""
RBAC权限控制系统
"""
from functools import wraps
from typing import Callable, Union

from fastapi import HTTPException, Request
from fastapi.responses import RedirectResponse

from admin.models import Admin, AdminRole


# 权限常量定义
class Permissions:
    """权限常量"""
    
    # 用户管理权限
    USER_VIEW = 'user.view'
    USER_CREATE = 'user.create'
    USER_EDIT = 'user.edit'
    USER_DELETE = 'user.delete'
    
    # 订单管理权限
    ORDER_VIEW = 'order.view'
    ORDER_CREATE = 'order.create'
    ORDER_EDIT = 'order.edit'
    ORDER_DELETE = 'order.delete'
    ORDER_REFUND = 'order.refund'
    
    # 展览管理权限
    EXHIBITION_VIEW = 'exhibition.view'
    EXHIBITION_CREATE = 'exhibition.create'
    EXHIBITION_EDIT = 'exhibition.edit'
    EXHIBITION_DELETE = 'exhibition.delete'
    
    # 展馆管理权限
    EXHIHALL_VIEW = 'exhihall.view'
    EXHIHALL_CREATE = 'exhihall.create'
    EXHIHALL_EDIT = 'exhihall.edit'
    EXHIHALL_DELETE = 'exhihall.delete'
    
    # 门票管理权限
    TICKET_VIEW = 'ticket.view'
    TICKET_CREATE = 'ticket.create'
    TICKET_EDIT = 'ticket.edit'
    TICKET_DELETE = 'ticket.delete'
    TICKET_CHECKIN = 'ticket.checkin'  # 验票权限
    
    # 管理员管理权限
    ADMIN_VIEW = 'admin.view'
    ADMIN_CREATE = 'admin.create'
    ADMIN_EDIT = 'admin.edit'
    ADMIN_DELETE = 'admin.delete'
    
    # 系统设置权限
    SETTING_VIEW = 'setting.view'
    SETTING_EDIT = 'setting.edit'


# 默认角色权限配置
DEFAULT_ROLE_PERMISSIONS = {
    AdminRole.SUPER_ADMIN: [
        # 拥有所有权限
        Permissions.USER_VIEW, Permissions.USER_CREATE, Permissions.USER_EDIT, Permissions.USER_DELETE,
        Permissions.ORDER_VIEW, Permissions.ORDER_CREATE, Permissions.ORDER_EDIT, Permissions.ORDER_DELETE, Permissions.ORDER_REFUND,
        Permissions.EXHIBITION_VIEW, Permissions.EXHIBITION_CREATE, Permissions.EXHIBITION_EDIT, Permissions.EXHIBITION_DELETE,
        Permissions.EXHIHALL_VIEW, Permissions.EXHIHALL_CREATE, Permissions.EXHIHALL_EDIT, Permissions.EXHIHALL_DELETE,
        Permissions.TICKET_VIEW, Permissions.TICKET_CREATE, Permissions.TICKET_EDIT, Permissions.TICKET_DELETE, Permissions.TICKET_CHECKIN,
        Permissions.ADMIN_VIEW, Permissions.ADMIN_CREATE, Permissions.ADMIN_EDIT, Permissions.ADMIN_DELETE,
        Permissions.SETTING_VIEW, Permissions.SETTING_EDIT,
    ],
    AdminRole.ADMIN: [
        # 除了删除订单和用户外的所有权限
        Permissions.USER_VIEW, Permissions.USER_CREATE, Permissions.USER_EDIT,
        Permissions.ORDER_VIEW, Permissions.ORDER_CREATE, Permissions.ORDER_EDIT, Permissions.ORDER_REFUND,
        Permissions.EXHIBITION_VIEW, Permissions.EXHIBITION_CREATE, Permissions.EXHIBITION_EDIT, Permissions.EXHIBITION_DELETE,
        Permissions.EXHIHALL_VIEW, Permissions.EXHIHALL_CREATE, Permissions.EXHIHALL_EDIT, Permissions.EXHIHALL_DELETE,
        Permissions.TICKET_VIEW, Permissions.TICKET_CREATE, Permissions.TICKET_EDIT, Permissions.TICKET_DELETE, Permissions.TICKET_CHECKIN,
        Permissions.ADMIN_VIEW, Permissions.ADMIN_CREATE, Permissions.ADMIN_EDIT, Permissions.ADMIN_DELETE,
        Permissions.SETTING_VIEW, Permissions.SETTING_EDIT,
    ],
    AdminRole.STORE_MANAGER: [
        # 只能管理自己门店的数据，可以验票，可以创建员工
        Permissions.USER_VIEW, Permissions.USER_CREATE, Permissions.USER_EDIT,
        Permissions.ORDER_VIEW, Permissions.ORDER_CREATE, Permissions.ORDER_EDIT, Permissions.ORDER_REFUND,
        Permissions.EXHIBITION_VIEW,
        Permissions.EXHIHALL_VIEW,
        Permissions.TICKET_VIEW, Permissions.TICKET_CHECKIN,
        Permissions.ADMIN_VIEW, Permissions.ADMIN_CREATE,
        Permissions.SETTING_VIEW,
    ],
    AdminRole.STAFF: [
        # 只能查看自己门店的数据，可以验票
        Permissions.USER_VIEW,
        Permissions.ORDER_VIEW,
        Permissions.EXHIBITION_VIEW,
        Permissions.EXHIHALL_VIEW,
        Permissions.TICKET_VIEW, Permissions.TICKET_CHECKIN,
        Permissions.ADMIN_VIEW,
        Permissions.SETTING_VIEW,
    ],
    AdminRole.OPERATOR: [
        # 只读权限，可以查看所有门店数据
        Permissions.USER_VIEW,
        Permissions.ORDER_VIEW,
        Permissions.EXHIBITION_VIEW,
        Permissions.EXHIHALL_VIEW,
        Permissions.TICKET_VIEW,
        Permissions.ADMIN_VIEW,
        Permissions.SETTING_VIEW,
    ],
}


def require_permission(permission: str, redirect_url: str = '/adm/'):
    """
    权限检查装饰器
    
    Args:
        permission: 需要的权限代码
        redirect_url: 权限不足时的重定向URL
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(status_code=500, detail="无法获取请求对象")
            
            user = request.user
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)
            
            # 检查用户是否激活
            if not user.is_active:
                raise HTTPException(status_code=403, detail="账号已被禁用")
            
            # 加载用户权限
            if not hasattr(user, '_permissions'):
                await user.load_permissions()
            
            # 检查权限
            if not user.has_permission(permission):
                if request.url.path.startswith('/adm/'):
                    return RedirectResponse(url=redirect_url, status_code=303)
                else:
                    raise HTTPException(status_code=403, detail="权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_role(*roles: AdminRole):
    """
    角色检查装饰器
    
    Args:
        roles: 允许的角色列表
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(status_code=500, detail="无法获取请求对象")
            
            user = request.user
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)
            
            # 检查用户是否激活
            if not user.is_active:
                raise HTTPException(status_code=403, detail="账号已被禁用")
            
            # 检查角色
            if user.role not in roles:
                if request.url.path.startswith('/adm/'):
                    return RedirectResponse(url='/adm/', status_code=303)
                else:
                    raise HTTPException(status_code=403, detail="角色权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def check_store_access(user: Admin, store_id: int) -> bool:
    """
    检查用户是否可以访问指定门店的数据
    
    Args:
        user: 管理员用户
        store_id: 门店ID
        
    Returns:
        bool: 是否有权限访问
    """
    if user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.OPERATOR]:
        return True
    return user.store_id == store_id


async def filter_stores_by_permission(user: Admin, store_ids: list[int] = None) -> list[int]:
    """
    根据用户权限过滤门店ID列表
    
    Args:
        user: 管理员用户
        store_ids: 门店ID列表，如果为None则返回用户有权限的所有门店
        
    Returns:
        list[int]: 用户有权限访问的门店ID列表
    """
    if user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.OPERATOR]:
        # 超级管理员、普通管理员和运营账号可以访问所有门店
        if store_ids is None:
            from apps.exhi.models import ExhiHall
            halls = await ExhiHall.all()
            return [hall.id for hall in halls]
        return store_ids
    elif user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
        # 店长和员工只能访问自己的门店
        if user.store_id:
            if store_ids is None:
                return [user.store_id]
            return [store_id for store_id in store_ids if store_id == user.store_id]
    
    return []
