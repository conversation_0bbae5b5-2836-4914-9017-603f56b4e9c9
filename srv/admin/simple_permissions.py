"""
简化的权限控制系统
基于角色直接判断权限，不使用复杂的RBAC模型
"""
from functools import wraps
from typing import Callable

from fastapi import HTTPException, Request
from fastapi.responses import RedirectResponse

from admin.models import Admin, AdminRole


def require_login(func: Callable):
    """
    要求用户登录的装饰器
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 从参数中获取request对象
        request = None
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break
        
        if not request:
            raise HTTPException(status_code=500, detail="无法获取请求对象")
        
        user = request.user
        if not isinstance(user, Admin):
            return RedirectResponse(url='/adm/login/', status_code=303)
        
        # 检查用户是否激活
        if not user.is_active:
            raise HTTPException(status_code=403, detail="账号已被禁用")
        
        return await func(*args, **kwargs)
    return wrapper


def require_role(*allowed_roles: AdminRole):
    """
    要求特定角色的装饰器
    
    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(status_code=500, detail="无法获取请求对象")
            
            user = request.user
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)
            
            # 检查用户是否激活
            if not user.is_active:
                raise HTTPException(status_code=403, detail="账号已被禁用")
            
            # 检查角色
            if user.role not in allowed_roles:
                if request.url.path.startswith('/adm/'):
                    return RedirectResponse(url='/adm/', status_code=303)
                else:
                    raise HTTPException(status_code=403, detail="角色权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_permission(permission_method: str):
    """
    要求特定权限的装饰器
    
    Args:
        permission_method: Admin模型中的权限检查方法名，如 'can_view_users'
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(status_code=500, detail="无法获取请求对象")
            
            user = request.user
            if not isinstance(user, Admin):
                return RedirectResponse(url='/adm/login/', status_code=303)
            
            # 检查用户是否激活
            if not user.is_active:
                raise HTTPException(status_code=403, detail="账号已被禁用")
            
            # 检查权限
            if not hasattr(user, permission_method):
                raise HTTPException(status_code=500, detail=f"权限方法 {permission_method} 不存在")
            
            permission_check = getattr(user, permission_method)
            if not permission_check():
                if request.url.path.startswith('/adm/'):
                    return RedirectResponse(url='/adm/', status_code=303)
                else:
                    raise HTTPException(status_code=403, detail="权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def check_store_permission(user: Admin, store_id: int) -> bool:
    """
    检查用户是否可以访问指定门店的数据
    
    Args:
        user: 管理员用户
        store_id: 门店ID
        
    Returns:
        bool: 是否有权限访问
    """
    return user.can_manage_store(store_id)


async def filter_stores_by_permission(user: Admin, store_ids: list[int] = None) -> list[int]:
    """
    根据用户权限过滤门店ID列表
    
    Args:
        user: 管理员用户
        store_ids: 门店ID列表，如果为None则返回用户有权限的所有门店
        
    Returns:
        list[int]: 用户有权限访问的门店ID列表
    """
    if user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.OPERATOR]:
        # 超级管理员、普通管理员和运营账号可以访问所有门店
        if store_ids is None:
            from apps.exhi.models import ExhiHall
            halls = await ExhiHall.all()
            return [hall.id for hall in halls]
        return store_ids
    elif user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
        # 店长和员工只能访问自己的门店
        if user.store_id:
            if store_ids is None:
                return [user.store_id]
            return [store_id for store_id in store_ids if store_id == user.store_id]
    
    return []


# 常用的权限装饰器组合
def require_user_view():
    """要求用户查看权限"""
    return require_permission('can_view_users')


def require_user_create():
    """要求用户创建权限"""
    return require_permission('can_create_users')


def require_user_edit():
    """要求用户编辑权限"""
    return require_permission('can_edit_users')


def require_user_delete():
    """要求用户删除权限"""
    return require_permission('can_delete_users')


def require_order_view():
    """要求订单查看权限"""
    return require_permission('can_view_orders')


def require_order_create():
    """要求订单创建权限"""
    return require_permission('can_create_orders')


def require_order_edit():
    """要求订单编辑权限"""
    return require_permission('can_edit_orders')


def require_order_delete():
    """要求订单删除权限"""
    return require_permission('can_delete_orders')


def require_ticket_checkin():
    """要求验票权限"""
    return require_permission('can_checkin_tickets')


def require_admin_manage():
    """要求管理员管理权限"""
    return require_permission('can_manage_admins')


def require_exhibition_manage():
    """要求展览管理权限"""
    return require_permission('can_manage_exhibitions')


def require_hall_manage():
    """要求展馆管理权限"""
    return require_permission('can_manage_halls')


def require_ticket_manage():
    """要求门票管理权限"""
    return require_permission('can_manage_tickets')


def require_settings_manage():
    """要求系统设置管理权限"""
    return require_permission('can_manage_settings')


# 角色装饰器快捷方式
def require_super_admin():
    """只允许超级管理员"""
    return require_role(AdminRole.SUPER_ADMIN)


def require_admin_or_above():
    """允许管理员及以上角色"""
    return require_role(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)


def require_manager_or_above():
    """允许店长及以上角色"""
    return require_role(AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER)


def require_staff_or_above():
    """允许员工及以上角色（除运营）"""
    return require_role(AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.STORE_MANAGER, AdminRole.STAFF)
