<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="后台管理系统" />
    <meta name="keywords" content="后台,管理,系统" />
    <title>「华夏漫游」管理系统</title>
    <link rel="icon"
          type="image/png"
          sizes="400x400"
          href="/static/img/favicon.png" />
    <link rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" />
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" />
    <link rel="stylesheet" href="/static/css/styles.css" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
      <div class="container-fluid">
        <nav aria-label="breadcrumb"
             class="d-flex align-items-center ms-3">
          <i class="fa-solid fa-location-dot me-2"></i>
          <ol class="breadcrumb mb-0">
            {% if breadcrumbs %}
              {% for crumb in breadcrumbs %}
                <li class="breadcrumb-item
                           {% if loop.last %}active{% endif %}"
                    {% if loop.last %}aria-current="page"{% endif %}>
                  {% if not loop.last %}
                    <a href="{{ crumb.url }}">{{ crumb.name }}</a>
                  {% else %}
                    <span>{{ crumb.name }}</span>
                  {% endif %}
                </li>
              {% endfor %}
            {% else %}
              <li class="breadcrumb-item active" aria-current="page">
                <a href="/adm/">首页</a>
              </li>
            {% endif %}
          </ol>
        </nav>
        <div class="collapse navbar-collapse">
          <ul class="navbar-nav ms-auto">
            {% if cfg.DEBUG %}
              <li class="nav-item">
                <span class="nav-link text-danger">
                  <i class="fa-solid fa-triangle-exclamation me-2"></i>当前为测试模式，请勿在线上环境使用
                </span>
              </li>
            {% endif %}
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle"
                 href="#"
                 id="navbarDropdown"
                 role="button"
                 data-bs-toggle="dropdown"
                 aria-expanded="false">
                <i class="fas fa-user me-1"></i>{{ request.user.display_name }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end"
                  aria-labelledby="navbarDropdown">
                <li>
                  <a class="dropdown-item" href="/adm/logout">退出登录</a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="main-container">
      <nav class="sidebar">
        <div class="sidebar-header">
          <img src="/static/img/blue-white.png"
               alt="Logo"
               class="img-fluid" />
          <h4>华夏漫游管理系统</h4>
        </div>
        <div class="position-sticky">
          <ul class="nav flex-column">
            <li class="nav-header">
              <i class="fas fa-chart-line me-2"></i>数据
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if request.url.path == '/adm/' %}active{% endif %}"
                 href="/adm/">
                <i class="fas fa-chart-pie fa-fw mx-2"></i>数据概览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'order' in request.url.path %}active{% endif %}"
                 href="/adm/order/">
                <i class="fas fa-ticket-alt fa-fw mx-2"></i>订单数据
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'user' in request.url.path %}active{% endif %}"
                 href="/adm/user/">
                <i class="fas fa-users fa-fw mx-2"></i>用户数据
              </a>
            </li>
            <li class="nav-header">
              <i class="fas fa-screwdriver-wrench me-2"></i>设置
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'settings' in request.url.path %}active{% endif %}"
                 href="/adm/settings/">
                <i class="fas fa-sliders fa-fw mx-2"></i>全局设置
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'exhibition' in request.url.path %}active{% endif %}"
                 href="/adm/exhibition/">
                <i class="fas fa-vr-cardboard fa-fw mx-2"></i>数字展览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'exhihall' in request.url.path %}active{% endif %}"
                 href="/adm/exhihall/">
                <i class="fas fa-university fa-fw mx-2"></i>展馆管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'ticket' in request.url.path %}active{% endif %}"
                 href="/adm/ticket/">
                <i class="fas fa-ticket fa-fw mx-2"></i>门票管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'joint' in request.url.path %}active{% endif %}"
                 href="/adm/joint/">
                <i class="fas fa-layer-group fa-fw mx-2"></i>联票管理
              </a>
            </li>
            <li class="nav-header">
              <i class="fas fa-gavel me-2"></i>权限
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'manager' in request.url.path %}active{% endif %}"
                 href="/adm/manager/">
                <i class="fas fa-user-shield fa-fw mx-2"></i>人员管理
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="content bg-sliver">

        {% block content %}
        {% endblock content %}

      </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    {% block ext_js %}
    {% endblock ext_js %}

  </body>
</html>
