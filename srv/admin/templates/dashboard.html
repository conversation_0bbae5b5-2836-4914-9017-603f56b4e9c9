{% extends "base.html" %}

{% block content %}
  <div class="container-fluid">
    <h2 class="h2">数据概览</h2>
    <p>欢迎来到后台管理系统！</p>

    <div class="row">
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
          <div class="card-body">
            <h5 class="card-title">用户总数</h5>
            <p class="card-text fs-4">{{ n_user }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-danger">
          <div class="card-body">
            <h5 class="card-title">订单总量</h5>
            <p class="card-text fs-4">{{ n_order }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
          <div class="card-body">
            <h5 class="card-title">昨日流水</h5>
            <p class="card-text fs-4">50 万</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
          <div class="card-body">
            <h5 class="card-title">本月总收入</h5>
            <p class="card-text fs-4">1000 万</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每日登录用户数</h5>
            <canvas id="userLoginChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每日新增用户数</h5>
            <canvas id="newUserChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">场馆收入对比</h5>
            <div style="height: 300px;" class="text-center">
              <canvas id="venueIncomeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每日收入趋势</h5>
            <div style="height: 300px;">
              <canvas id="dailyIncomeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每月收入统计</h5>
            <canvas id="monthlyIncomeChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block ext_js %}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
  const chartData = {{ chart_data|safe }};

  // 每日登录用户数
  new Chart(document.getElementById('userLoginChart'), {
    type: 'line',
    data: {
      labels: chartData.dates,
      datasets: [{
        label: '登录用户数',
        data: chartData.daily_logins,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }]
    }
  });

  // 每日新增用户数
  new Chart(document.getElementById('newUserChart'), {
    type: 'bar',
    data: {
      labels: chartData.dates,
      datasets: [{
        label: '新增用户数',
        data: chartData.daily_new_users,
        backgroundColor: 'rgb(255, 99, 132)'
      }]
    }
  });

  // 场馆收入对比
  new Chart(document.getElementById('venueIncomeChart'), {
    type: 'pie',
    data: {
      labels: Object.keys(chartData.venue_income),
      datasets: [{
        data: Object.values(chartData.venue_income),
        backgroundColor: [
          'rgb(255, 99, 132)',
          'rgb(54, 162, 235)',
          'rgb(255, 205, 86)',
          'rgb(75, 192, 192)'
        ]
      }]
    }
  });

  // 每日收入趋势
  new Chart(document.getElementById('dailyIncomeChart'), {
    type: 'line',
    data: {
      labels: chartData.dates,
      datasets: [{
        label: '每日收入',
        data: chartData.daily_income,
        borderColor: 'rgb(54, 162, 235)',
        tension: 0.1
      }]
    }
  });

  // 每月收入统计
  new Chart(document.getElementById('monthlyIncomeChart'), {
    type: 'bar',
    data: {
      labels: chartData.monthly_income.months,
      datasets: [{
        label: '月收入',
        data: chartData.monthly_income.data,
        backgroundColor: 'rgb(153, 102, 255)'
      }]
    }
  });
  </script>
{% endblock ext_js %}
