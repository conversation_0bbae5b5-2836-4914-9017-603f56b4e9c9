{% extends "base.html" %}

{% block content %}
  <h2>展馆管理</h2>

  <form method="post" action="/adm/exhihall/save/">
    {% if hall %}
      <input type="hidden" name="hid" value="{{ hall.id }}" />
    {% endif %}
    <div class="mb-3 col-md-6">
      <label for="name" class="form-label">展馆名称</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="name"
             name="name"
             value="{{ hall.name if hall else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="city" class="form-label">城市</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="city"
             name="city"
             value="{{ hall.city if hall else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="addr" class="form-label">详细地址</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="addr"
             name="addr"
             value="{{ hall.addr if hall else '' }}"
             required />
    </div>
    <div class="mb-3">
      <label for="notice" class="form-label">入馆公告</label>
      <textarea class="form-control border-primary-subtle" id="notice" name="notice" rows="5">{{ hall.notice if hall else '' }}</textarea>
    </div>
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/exhihall/" class="btn btn-secondary">取消</a>
  </form>
{% endblock content %}
