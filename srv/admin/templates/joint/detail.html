{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">
      <span class="text-primary fw-bold">{{ joint.title }}</span>
    </h2>
    <div class="btn-toolbar mb-2 ms-2">
      <a href="/adm/joint/form/?jid={{ joint.id }}"
         class="btn btn-sm btn-outline-primary">
        <i class="fas fa-edit"></i>
        修改
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <table class="table table-striped align-middle">
        <tbody>
          <tr>
            <th scope="row" class="w-25 text-start">标题</th>
            <td class="px-3">{{ joint.title }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展馆</th>
            <td class="px-3">
              <a href="/adm/exhihall/detail/?hid={{ joint.exhihall.id }}">{{ joint.exhihall.city }} · {{ joint.exhihall.name }}</a>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">类型</th>
            <td class="px-3">
              <span class="badge bg-warning fs-6">联票</span>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">展期</th>
            <td class="px-3">{{ joint.start }} 至 {{ joint.end }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">包含展票</th>
            <td class="px-3">
              <ul class="list-unstyled mb-0">
                {% for ticket in joint.tickets %}
                  <li>
                    <a href="/adm/ticket/detail/?tid={{ ticket.id }}">{{ ticket.title }}</a>
                  </li>
                {% endfor %}
              </ul>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">票价</th>
            <td class="px-3 py-0">
              <table class="table table-hover align-middle mb-0">
                <tbody>
                  {% for timeslot, grades in joint.prices.items() %}
                    {% for grade, price in grades %}
                      <tr>
                        {% if loop.first %}<td rowspan="{{ grades|length }}">{{ timeslot }}</td>{% endif %}
                        <td>{{ grade }}</td>
                        <td class="text-end font-monospace">¥{{ price }}</td>
                      </tr>
                    {% endfor %}
                  {% endfor %}
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row mt-5 justify-content-around">
    <div class="col-md-2 text-center">
      <h5>缩略图</h5>
      <hr />
      <img src="{{ joint.thumbnail }}"
           alt="{{ joint.title }}"
           class="img-fluid rounded" />
    </div>
    <div class="col-md-6 text-center">
      <h5>概览图</h5>
      <hr />
      <img src="{{ joint.banner }}"
           alt="{{ joint.title }}"
           class="img-fluid rounded" />
    </div>
  </div>
{% endblock content %}
