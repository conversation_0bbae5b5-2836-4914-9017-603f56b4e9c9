{% extends "base.html" %}

{% block content %}
  <h2 class="mb-4">{{ '编辑' if is_edit else '添加' }}联票</h2>

  <form method="post"
        action="/adm/joint/save/"
        enctype="multipart/form-data"
        id="jointForm">
    {% if is_edit %}<input type="hidden" name="jid" value="{{ joint.id }}" />{% endif %}
    <div class="mb-3 col-md-6">
      <label for="title" class="form-label">标题</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="title"
             name="title"
             value="{{ joint.title if joint else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="hid" class="form-label">展馆</label>
      <select class="form-select border-primary-subtle"
              id="hid"
              name="hid"
              required>
        {% for hall in halls %}
          <option value="{{ hall.id }}"
                  {% if is_edit and joint.hid == hall.id %}selected{% endif %}>【{{ hall.city }}】{{ hall.name }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="mb-3 col-md-6">
      <label for="tids" class="form-label">包含的展票 (至少选择 2 项)</label>
      <div id="tids" class="form-control border-primary-subtle">
        {% for ticket in tickets %}
          <div class="form-check">
            <input class="form-check-input"
                   type="checkbox"
                   name="tids"
                   value="{{ ticket.id }}"
                   id="ticket{{ ticket.id }}"
                   {% if is_edit and ticket.id in joint.tids %}checked{% endif %} />
            <label class="form-check-label" for="ticket{{ ticket.id }}">{{ ticket.title }}</label>
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- 新增字段 -->
    <div class="mb-3 col-md-6">
      <label for="start" class="form-label">联票开始日期</label>
      <input type="date"
             class="form-control border-primary-subtle"
             id="start"
             name="start"
             min="{{ today }}"
             value="{{ joint.start|string if is_edit and joint.start else today }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="end" class="form-label">联票结束日期</label>
      <input type="date"
             class="form-control border-primary-subtle"
             id="end"
             name="end"
             min="{{ today }}"
             value="{{ joint.end|string if is_edit and joint.end else today }}"
             required />
    </div>

    <!-- 新增字段结束 -->
    <div class="mb-3 col-md-6">
      <label for="thumbnail" class="form-label">缩略图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="thumbnail"
             name="thumbnail"
             accept="image/jpeg,image/png"
             {% if not is_edit %}required{% endif %} />
      {% if is_edit and joint.thumbnail %}
        <img src="{{ joint.thumbnail }}"
             alt="Thumbnail"
             width="100"
             class="mt-2" />
        <input type="hidden"
               name="thumbnail_path"
               value="{{ joint.thumbnail }}" />
      {% endif %}
    </div>
    <div class="mb-3 col-md-6">
      <label for="banner" class="form-label">概览图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="banner"
             name="banner"
             accept="image/jpeg,image/png"
             {% if not is_edit %}required{% endif %} />
      {% if is_edit and joint.banner %}
        <img src="{{ joint.banner }}"
             alt="Banner"
             width="200"
             class="mt-2" />
        <input type="hidden" name="banner_path" value="{{ joint.banner }}" />
      {% endif %}
    </div>

    <!-- 票价设置区域 -->
    <div class="mb-4">
      <h4>票价设置</h4>
      <div id="priceContainer">
        <!-- 动态生成的票价设置内容 -->
      </div>
      <button type="button"
              class="btn btn-outline-primary btn-sm"
              id="addTimeslotBtn">
        <i class="fas fa-plus"></i> 增加场次
      </button>
    </div>

    <input type="hidden" name="prices" id="pricesInput" />
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/joint/" class="btn btn-secondary">取消</a>
  </form>

  <script>
    // 预定义的场次和票档选项
    const TIMESLOTS = ['全通票', '平日票', '早鸟票', '特惠票'];
    const GRADES = ['单人', '双人', '三人', '儿童', '亲子（1大1小）', '家庭（2大2小）', '学生', '军人', '老年人'];

    // 存储当前票价数据
    let priceData = {};

    // 初始化现有数据
    const priceJson = '{{ joint.prices | tojson | safe if is_edit and joint.prices else "{}" }}';
    priceData = JSON.parse(priceJson);

    // 生成场次选择器HTML
    function createTimeslotSelect(selectedValue = '') {
      let options = TIMESLOTS.map(
        (slot) => `<option value="${slot}" ${slot === selectedValue ? 'selected' : ''}>${slot}</option>`
      ).join('');
      options += `<option value="custom" ${!TIMESLOTS.includes(selectedValue) && selectedValue ? 'selected' : ''}>自定义</option>`;

      return `
        <select class="form-select form-select-sm timeslot-select" style="width: 258px;">
          <option value="">选择场次</option>
          ${options}
        </select>
      `;
    }

    // 生成票档选择器HTML
    function createGradeSelect(selectedValue = '') {
      let options = GRADES.map(
        (grade) => `<option value="${grade}" ${grade === selectedValue ? 'selected' : ''}>${grade}</option>`
      ).join('');
      options += `<option value="custom" ${!GRADES.includes(selectedValue) && selectedValue ? 'selected' : ''}>自定义</option>`;

      return `
        <select class="form-select form-select-sm grade-select" style="width: 150px;">
          <option value="">选择票档</option>
          ${options}
        </select>
      `;
    }

    // 创建票档行HTML
    function createGradeRow(timeslot, grade = '', price = '', isFirst = false, isLast = false) {
      const isCustomGrade = grade && !GRADES.includes(grade);

      return `
        <div class="grade-row d-flex align-items-center mb-2">
          ${createGradeSelect(grade)}
          <input type="text" class="form-control form-control-sm ms-2 custom-grade-input" placeholder="自定义票档" value="${isCustomGrade ? grade : ''}" style="width: 120px; display: ${isCustomGrade ? 'block' : 'none'};" />
          <input type="number" class="form-control form-control-sm ms-2 price-input" placeholder="价格" value="${price}" step="0.01" min="0" style="width: 100px;" />
          <button type="button" class="btn btn-outline-danger btn-sm ms-2 remove-grade-btn">
            <i class="fas fa-times"></i>
          </button>
          ${
            isLast
            ? `<button type="button" class="btn btn-outline-success btn-sm ms-1 add-grade-btn"><i class="fas fa-plus"></i></button>`
            : ''
          }
        </div>
      `;
    }

    // 创建场次行HTML
    function createTimeslotRow(timeslot = '', grades = []) {
      const isCustomTimeslot = timeslot && !TIMESLOTS.includes(timeslot);
      let gradeRows = '';

      if (grades.length > 0) {
        gradeRows = grades
          .map(([grade, price], index) =>
            createGradeRow(timeslot, grade, price, index === 0, index === grades.length - 1)
          )
          .join('');
      } else {
        gradeRows = createGradeRow(timeslot, '', '', true, true);
      }

      return `
        <div class="timeslot-group border rounded p-3 mb-3">
          <div class="d-flex align-items-center mb-2">
            ${createTimeslotSelect(timeslot)}
            <input type="text" class="form-control form-control-sm ms-2 custom-timeslot-input" placeholder="自定义场次" value="${isCustomTimeslot ? timeslot : ''}" style="width: 120px; display: ${isCustomTimeslot ? 'block' : 'none'};" />
            <button type="button" class="btn btn-outline-danger btn-sm ms-auto remove-timeslot-btn">
              删除场次
            </button>
          </div>
          <div class="grades-container">
            ${gradeRows}
          </div>
        </div>
      `;
    }

    // 渲染所有票价设置
    function renderPriceSettings() {
      const container = document.getElementById('priceContainer');
      let html = '';

      if (Object.keys(priceData).length === 0) {
        html = createTimeslotRow();
      } else {
        for (const [timeslot, grades] of Object.entries(priceData)) {
          html += createTimeslotRow(timeslot, grades);
        }
      }

      container.innerHTML = html;
      bindEvents();
    }

    // 绑定事件
    function bindEvents() {
      // 场次选择变化
      document.querySelectorAll('.timeslot-select').forEach((select) => {
        select.addEventListener('change', function() {
          const customInput = this.parentNode.querySelector('.custom-timeslot-input');
          if (this.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
          } else {
            customInput.style.display = 'none';
            customInput.value = '';
          }
        });
      });

      // 票档选择变化
      document.querySelectorAll('.grade-select').forEach((select) => {
        select.addEventListener('change', function() {
          const customInput = this.parentNode.querySelector('.custom-grade-input');
          if (this.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
          } else {
            customInput.style.display = 'none';
            customInput.value = '';
          }
        });
      });

      // 删除场次
      document.querySelectorAll('.remove-timeslot-btn').forEach((btn) => {
        btn.addEventListener('click', function() {
          this.closest('.timeslot-group').remove();
        });
      });

      // 删除票档
      document.querySelectorAll('.remove-grade-btn').forEach((btn) => {
        btn.addEventListener('click', function() {
          const gradeRow = this.closest('.grade-row');
          const gradesContainer = gradeRow.parentNode;
          const isLast = gradeRow.querySelector('.add-grade-btn') !== null;

          gradeRow.remove();

          // 如果没有票档了，添加一个空的
          if (gradesContainer.children.length === 0) {
            gradesContainer.innerHTML = createGradeRow('', '', '', true, true);
            bindEvents();
          } else if (isLast) {
            // 如果删除的是最后一个票档，需要给新的最后一个票档添加加号按钮
            const newLastRow = gradesContainer.lastElementChild;
            const addBtn = document.createElement('button');
            addBtn.type = 'button';
            addBtn.className = 'btn btn-outline-success btn-sm ms-1 add-grade-btn';
            addBtn.innerHTML = '<i class="fas fa-plus"></i>';
            newLastRow.appendChild(addBtn);
            bindEvents();
          }
        });
      });

      // 添加票档
      document.querySelectorAll('.add-grade-btn').forEach((btn) => {
        btn.addEventListener('click', function() {
          const gradesContainer = this.closest('.timeslot-group').querySelector('.grades-container');

          // 移除当前最后一个票档的加号按钮
          const currentLastRow = this.closest('.grade-row');
          const addBtn = currentLastRow.querySelector('.add-grade-btn');
          if (addBtn) {
            addBtn.remove();
          }

          // 添加新的票档行（作为新的最后一行，带加号按钮）
          const newRow = document.createElement('div');
          newRow.innerHTML = createGradeRow('', '', '', false, true);
          gradesContainer.appendChild(newRow.firstElementChild);
          bindEvents();
        });
      });
    }

    // 添加场次
    document.getElementById('addTimeslotBtn').addEventListener('click', function() {
      const container = document.getElementById('priceContainer');
      const newGroup = document.createElement('div');
      newGroup.innerHTML = createTimeslotRow();
      container.appendChild(newGroup.firstElementChild);
      bindEvents();
    });

    // 表单提交处理
    document.getElementById('jointForm').addEventListener('submit', function(e) {
      const checkedExhibitions = document.querySelectorAll('input[name="tids"]:checked').length;
      if (checkedExhibitions < 2) {
        e.preventDefault();
        alert('请至少选择 2 个展览');
        return;
      }

      // 收集表单数据到 FormData
      const formData = new FormData(this);

      // 收集票价数据
      const prices = {};
      document.querySelectorAll('.timeslot-group').forEach((group) => {
        const timeslotSelect = group.querySelector('.timeslot-select');
        const customTimeslotInput = group.querySelector('.custom-timeslot-input');

        let timeslot = timeslotSelect.value;
        if (timeslot === 'custom') {
          timeslot = customTimeslotInput.value.trim();
        }

        if (!timeslot) return;

        const grades = [];
        group.querySelectorAll('.grade-row').forEach((row) => {
          const gradeSelect = row.querySelector('.grade-select');
          const customGradeInput = row.querySelector('.custom-grade-input');
          const priceInput = row.querySelector('.price-input');

          let grade = gradeSelect.value;
          if (grade === 'custom') {
            grade = customGradeInput.value.trim();
          }

          const price = parseFloat(priceInput.value);

          if (grade && !isNaN(price) && price >= 0) {
            grades.push([grade, price]);
          }
        });

        if (grades.length > 0) {
          prices[timeslot] = grades;
        }
      });

      document.getElementById('pricesInput').value = JSON.stringify(prices);

      // 提交表单
      this.submit();
    });

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderPriceSettings();
    });
  </script>
{% endblock content %}
