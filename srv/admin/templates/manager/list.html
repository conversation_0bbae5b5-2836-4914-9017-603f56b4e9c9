{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">成员管理</h2>
    <div class="btn-toolbar mb-2 ms-2">
      <a href="/adm/manager/form/"
         class="btn btn-sm btn-outline-primary">
        <i class="fas fa-plus"></i>
        新增管理员
      </a>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table table-striped table-hover table-bordered table-sm">
      <thead>
        <tr>
          <th class="px-3 text-center">ID</th>
          <th class="px-3">用户名</th>
          <th class="px-3">手机号</th>
          <th class="px-3">创建时间</th>
          <th class="px-3">操作</th>
        </tr>
      </thead>
      <tbody>
        {% for manager in managers %}
          <tr>
            <td class="px-3 text-center">{{ manager.id }}</td>
            <td class="px-3">{{ manager.username }}</td>
            <td class="px-3">{{ manager.phone or '' }}</td>
            <td class="px-3">{{ manager.created.strftime("%Y-%m-%d %H:%M:%S") }}</td>
            <td class="px-3">
              <div class="btn-group">
                <a href="/adm/manager/form/?manager_id={{ manager.id }}"
                   class="btn btn-sm btn-warning">修改</a>
                <a href="/adm/manager/delete/?manager_id={{ manager.id }}"
                   class="btn btn-sm btn-danger"
                   onclick="return confirm('确定要删除吗？');">删除</a>
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
{% endblock content %}
