{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">订单数据</h2>
    {% if cfg.DEBUG %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/order/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增订单
        </a>
      </div>
    {% endif %}
  </div>

  {% if orders %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm">
        <thead>
          <tr>
            <th class="px-3">商户订单号</th>
            <th class="px-3">用户手机号</th>
            <th class="px-3 text-center">订单状态</th>
            <th class="px-3 text-end">订单价格 (元)</th>
            <th class="px-3">场次</th>
            <th class="px-3">票档</th>
            <th class="px-3">操作</th>
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td class="px-3 font-monospace">{{ order.trade_no }}</td>
              <td class="px-3">{{ order.phone }}</td>
              {% if order.status == '待使用' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-success px-3">{{ order.status }}</span>
                </td>
              {% elif order.status == '待支付' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-info px-3">{{ order.status }}</span>
                </td>
              {% elif order.status == '已使用' %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-dark px-3">{{ order.status }}</span>
                </td>
              {% else %}
                <td class="px-3 text-center">
                  <span class="badge text-bg-secondary px-3">{{ order.status }}</span>
                </td>
              {% endif %}
              <td class="px-3 text-end">{{ order.amount }}</td>
              <td class="px-3">{{ order.timeslot }}</td>
              <td class="px-3">{{ order.grade }}</td>
              <td class="px-3">
                <div class="btn-group">
                  {% if order.status == '待使用' %}
                    <a href="/adm/order/form/?order_id={{ order.id }}"
                       class="btn btn-sm btn-primary">核销</a>
                  {% endif %}

                  <a href="/adm/order/form/?order_id={{ order.id }}"
                     class="btn btn-sm btn-warning">修改</a>
                  <a href="/adm/order/delete/?order_id={{ order.id }}"
                     class="btn btn-sm btn-danger"
                     onclick="return confirm('确定要删除吗？');">删除</a>

                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无订单
    </div>
  {% endif %}
{% endblock content %}
