{% extends "base.html" %}

{% block content %}
  <h2 class="h2">用户管理</h2>

  <form method="post" action="/adm/user/save/">
    {% if user %}<input type="hidden" name="user_id" value="{{ user.id }}" />{% endif %}
    <div class="mb-3 col-md-6">
      <label for="name" class="form-label">用户名</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="name"
             name="name"
             value="{{ user.name if user else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="openid" class="form-label">Open ID</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="openid"
             name="openid"
             value="{{ user.openid if user else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="phone" class="form-label">手机号</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="phone"
             name="phone"
             value="{{ user.phone or '' }}" />
    </div>
    <div class="mb-3 col-md-6">
      <label for="is_adm" class="form-label">
        管理员账号 <span class="text-muted fs-7">（开启后可核销门票）</span>
      </label>
      <div class="form-check form-switch">
        <input class="form-check-input"
               type="checkbox"
               role="switch"
               id="is_adm"
               name="is_adm"
               {% if user and user.is_adm %}checked{% endif %} />
      </div>
    </div>
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/user/" class="btn btn-secondary">取消</a>
  </form>
{% endblock content %}
