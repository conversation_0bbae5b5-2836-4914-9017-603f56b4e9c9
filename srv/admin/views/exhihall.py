from fastapi import Depends, Form, Query, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from apps.exhi.models import ExhiHall, Joint, Ticket

__all__ = ['exhihall_delete', 'exhihall_detail', 'exhihall_form', 'exhihall_list', 'exhihall_save']


@router.get('/exhihall/')
async def exhihall_list() -> Response:
    """展馆列表页"""
    halls = await ExhiHall.all()
    return render_html('exhihall/list.html', {'halls': halls})


@router.get('/exhihall/detail/')
async def exhihall_detail(hid: int) -> Response:
    """展览详情页"""
    hall = await ExhiHall.get(id=hid)
    tickets = [await tk.prefetch() for tk in await Ticket.filter(hid=hall.id)]
    joints = [await jt.prefetch() for jt in await Joint.filter(hid=hall.id)]
    return render_html('exhihall/detail.html', {'hall': hall, 'tickets': tickets, 'joints': joints})


@router.get('/exhihall/form/')
async def exhihall_form(hid: int | None = Query(None)) -> Response:
    """展馆创建和编辑页"""
    hall, is_edit = (await ExhiHall.get_or_none(id=hid), True) if hid else (None, False)
    return render_html('exhihall/form.html', {'hall': hall, 'is_edit': is_edit})


@router.post('/exhihall/save/')
async def exhihall_save(
    form: sch.ExhiHallForm = Depends(sch.ExhiHallForm.load),
    hid: int | None = Form(None),
) -> Response:
    """处理展馆创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    if hid:
        await ExhiHall.filter(id=hid).update(**update_data)
        await ExhiHall.clear_cache(hid)
    else:
        hid = (await ExhiHall.create(**update_data)).id

    return Redirect(url=f'/adm/exhihall/detail/?hid={hid}', status_code=303)


@router.get('/exhihall/delete/')
async def exhihall_delete(hid: int) -> Response:
    """删除指定展馆"""
    await (await ExhiHall.get(id=hid)).delete()
    return Redirect(url='/adm/exhihall/', status_code=303)
