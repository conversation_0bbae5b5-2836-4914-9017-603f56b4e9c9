import json
import random
import time
from datetime import datetime, timedelta

from fastapi import Form, Request, Response
from fastapi.responses import RedirectResponse as Redirect
from jose import jwt
from tortoise.exceptions import DoesNotExist

from admin.http import render_html, router
from admin.models import Admin
from apps.exhi.models import Exhibition, ExhiHall
from apps.order.models import Order
from apps.user.models import User
from config import DEBUG, DOMAIN, JWT_ALGORITHM, SECRET_KEY, SESSION_MAX_AGE

__all__ = ['dashboard', 'login', 'login_page', 'logout']


def generate_daily_data(days=30):
    """生成最近30天的每日数据"""
    today = datetime.now()
    dates = [(today - timedelta(days=x)).strftime('%m-%d') for x in range(days)]
    dates.reverse()
    return dates


def generate_random_data():
    """生成图表所需的随机数据"""
    dates = generate_daily_data()

    # 生成每日登录用户数和新增用户数
    daily_logins = [random.randint(100, 500) for _ in dates]
    daily_new_users = [random.randint(10, 50) for _ in dates]

    # 生成场馆收入数据
    venue_income = {
        '鼓楼': random.randint(10000, 20000),
        '博物馆': random.randint(8000, 15000),
        '剑门关': random.randint(5000, 12000),
        '滕王阁': random.randint(15000, 25000),
    }

    # 生成每日收入数据
    daily_income = [random.randint(1000, 5000) for _ in dates]

    # 生成每月收入数据
    monthly_income = [random.randint(50000, 150000) for _ in range(12)]
    months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

    return {
        'dates': dates,
        'daily_logins': daily_logins,
        'daily_new_users': daily_new_users,
        'venue_income': venue_income,
        'daily_income': daily_income,
        'monthly_income': {'months': months, 'data': monthly_income},
    }


@router.get('/login')
async def login_page(request: Request) -> Response:
    """渲染后台登录页面"""
    if request.user.is_authenticated:
        return Redirect(url='/adm/', status_code=303)
    return render_html('login.html')


@router.post('/login')
async def login(request: Request, username: str = Form(...), password: str = Form(...)) -> Response:
    """处理后台登录表单，校验用户名和密码，登录成功后跳转到后台主页"""
    error = None
    try:
        manager = await Admin.get(username=username)
        if not manager.verify_password(password):
            error = '用户名或密码错误'
        else:
            user_info = {'uid': manager.id, 'username': manager.username, 'is_adm': True}
            now = int(time.time())
            jwt_token = jwt.encode({**user_info, 'iat': now, 'exp': now + SESSION_MAX_AGE}, SECRET_KEY, JWT_ALGORITHM)
            response = Redirect(url='/adm/', status_code=303)
            response.set_cookie(
                'Authorization',
                f'Bearer {jwt_token}',
                max_age=SESSION_MAX_AGE,
                domain=None if DEBUG else f'.{DOMAIN}',
                secure=not DEBUG,
                httponly=False,
                samesite='lax',
            )
            return response
    except DoesNotExist:
        error = '用户名或密码错误'

    return render_html('login.html', {'error': error})


@router.get('/logout')
async def logout() -> Response:
    """注销后台登录，清除 session 并跳转到登录页"""
    response = Redirect(url='/adm/login', status_code=303)
    response.delete_cookie('Authorization', domain=None if DEBUG else f'.{DOMAIN}', samesite='lax')
    return response


@router.get('/')
async def dashboard() -> Response:
    """后台首页仪表盘"""
    chart_data = generate_random_data()
    context = {
        'n_user': await User.all().count(),
        'n_order': await Order.all().count(),
        'n_exhibition': await Exhibition.all().count(),
        'n_exhihall': await ExhiHall.all().count(),
        'chart_data': json.dumps(chart_data),
    }
    return render_html('dashboard.html', context)
