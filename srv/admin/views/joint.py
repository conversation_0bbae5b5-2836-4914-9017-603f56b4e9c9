import json
from datetime import date

from fastapi import File, Form, HTTPException, Query, Response, UploadFile
from fastapi.responses import RedirectResponse as Redirect

from admin.http import render_html, router
from admin.schemas import JointForm
from apps.exhi.models import ExhiHall, Joint, Ticket
from config import ALLOWED_TYPES, BANNER_PREFIX, MAX_SIZE, THUMBNAIL_PREFIX
from libs.utils import file_type, save_upfile

__all__ = ['joint_delete', 'joint_detail', 'joint_form', 'joint_list', 'joint_save']


@router.get('/joint/')
async def joint_list() -> Response:
    """联票列表页"""
    joints = []
    for j in await Joint.all():
        await j.prefetch()
        for t in j.tickets:
            await t.prefetch()
        joints.append(j)

    return render_html('joint/list.html', {'joints': joints})


@router.get('/joint/detail/')
async def joint_detail(jid: int) -> Response:
    """联票详情页"""
    joint = await Joint.get(id=jid)
    await joint.prefetch()
    for t in joint.tickets:
        await t.prefetch()
    return render_html('joint/detail.html', {'joint': joint})


@router.get('/joint/form/')
async def joint_form(jid: int | None = Query(None), hid: int | None = Query(None)) -> Response:
    """联票创建和编辑页"""
    joint, is_edit = (await Joint.get_or_none(id=jid), True) if jid else (None, False)
    if joint:
        hid = joint.hid
    halls = [await ExhiHall.get(id=hid)] if hid else await ExhiHall.all()
    tickets = await Ticket.in_times(hid=hid) if hid else await Ticket.in_times()
    return render_html(
        'joint/form.html',
        {
            'joint': joint,
            'is_edit': is_edit,
            'halls': halls,
            'tickets': tickets,
            'today': date.today().isoformat(),
        },
    )


@router.post('/joint/save/')
async def joint_save(  # noqa: C901
    title: str = Form(..., max_length=64),
    hid: int = Form(...),
    tids: list[int] = Form(...),
    start: str = Form(...),
    end: str = Form(...),
    prices: str = Form('{}'),
    thumbnail: UploadFile = File(None),
    banner: UploadFile = File(None),
    thumbnail_path: str = Form(None),
    banner_path: str = Form(None),
    jid: int | None = Form(None),
) -> Response:
    """处理联票创建和编辑表单"""
    form_data = {
        'title': title,
        'hid': hid,
        'tids': tids,
        'prices': json.loads(prices),
        'start': start,  # 新增
        'end': end,  # 新增
        'jid': jid,
    }
    form = JointForm.load_from_json(form_data)

    for upfile in [thumbnail, banner]:
        if not hasattr(upfile, 'filename') or not upfile.filename:
            continue
        extension = file_type(upfile)
        if extension not in ALLOWED_TYPES:
            raise HTTPException(status_code=400, detail=f'文件类型 {extension} 不支持，仅支持 jpg/png')
        fsz = upfile.size or 0
        if not (0 < fsz <= MAX_SIZE):
            raise HTTPException(
                status_code=400, detail=f'文件 {upfile.filename} 大小超过限制: {round(fsz / 1024 / 1024, 1)}MB'
            )

    update_data = {
        'title': form.title,
        'hid': form.hid,
        'tids': form.tids,
        'start': form.start,  # 新增
        'end': form.end,  # 新增
        'prices': form.prices,
    }

    # 文件类型和大小验证
    for upfile in [thumbnail, banner]:
        if upfile and hasattr(upfile, 'filename') and upfile.filename:
            extension = file_type(upfile)
            if extension not in ALLOWED_TYPES:
                raise HTTPException(status_code=400, detail=f'文件类型 {extension} 不支持，仅支持 jpg/png')
            fsz = upfile.size or 0
            if not (0 < fsz <= MAX_SIZE):
                raise HTTPException(
                    status_code=400, detail=f'文件 {upfile.filename} 大小超过限制: {round(fsz / 1024 / 1024, 1)}MB'
                )

    if thumbnail and hasattr(thumbnail, 'filename') and thumbnail.filename:
        update_data['thumbnail'] = await save_upfile(thumbnail, THUMBNAIL_PREFIX)
    elif thumbnail_path:
        update_data['thumbnail'] = thumbnail_path

    if banner and hasattr(banner, 'filename') and banner.filename:
        update_data['banner'] = await save_upfile(banner, BANNER_PREFIX)
    elif banner_path:
        update_data['banner'] = banner_path

    if form.jid:
        await Joint.filter(id=form.jid).update(**update_data)
        await Joint.clear_cache(form.jid)
        new_jid = form.jid
    else:
        joint = await Joint.create(**update_data)
        new_jid = joint.id

    return Redirect(url=f'/adm/joint/detail/?jid={new_jid}', status_code=303)


@router.get('/joint/delete/')
async def joint_delete(jid: int) -> Response:
    """删除指定联票"""
    await (await Joint.get(id=jid)).delete()
    return Redirect(url='/adm/joint/', status_code=303)
