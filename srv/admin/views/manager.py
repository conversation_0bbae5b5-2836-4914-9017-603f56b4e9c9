from fastapi import HTT<PERSON>Exception, Request, Response

from admin.http import render_html, router
from admin.models import Admin, AdminRole
from admin.permissions import Permissions, filter_stores_by_permission, require_permission
from apps.exhi.models import ExhiHall

__all__ = ['manager_delete', 'manager_form', 'manager_list', 'manager_save']


@router.get('/manager/')
@require_permission(Permissions.ADMIN_VIEW)
async def manager_list(request: Request) -> Response:
    """管理员用户列表页"""
    current_user = request.user

    # 根据用户角色过滤管理员列表
    if current_user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]:
        managers = await Admin.all()
    elif current_user.role == AdminRole.STORE_MANAGER:
        # 店长只能看到自己门店的员工和自己
        managers = await Admin.filter(
            store_id=current_user.store_id, role__in=[AdminRole.STORE_MANAGER, AdminRole.STAFF]
        )
    else:
        # 员工和运营只能看到自己
        managers = [current_user]

    return render_html('manager/list.html', {'managers': managers, 'current_user': current_user})


@router.get('/manager/form/')
@require_permission(Permissions.ADMIN_CREATE)
async def manager_form(request: Request, manager_id: int | None = None) -> Response:
    """管理员用户创建和编辑页"""
    current_user = request.user

    # 获取可用的门店列表
    store_ids = await filter_stores_by_permission(current_user)
    stores = await ExhiHall.filter(id__in=store_ids) if store_ids else []

    # 获取可创建的角色列表
    available_roles = []
    for role in AdminRole:
        if current_user.can_create_role(role):
            available_roles.append(role)

    manager = None
    is_edit = False

    if manager_id:
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查是否有权限编辑此管理员
        if current_user.role == AdminRole.STORE_MANAGER:
            if manager.store_id != current_user.store_id:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        is_edit = True

    context = {
        'manager': manager,
        'is_edit': is_edit,
        'stores': stores,
        'available_roles': available_roles,
        'current_user': current_user,
        'AdminRole': AdminRole,
    }

    return render_html('manager/form.html', context)
