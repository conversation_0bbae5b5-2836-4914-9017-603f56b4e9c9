from fastapi import Depends, Form, HTTPException, Request, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin, AdminRole
from admin.permissions import check_store_permission, filter_stores_by_permission, require_admin_manage
from apps.exhi.models import ExhiHall

__all__ = ['manager_delete', 'manager_form', 'manager_list', 'manager_save']


@router.get('/manager/')
@require_admin_manage()
async def manager_list(request: Request) -> Response:
    """管理员用户列表页"""
    current_user = request.user

    # 根据用户角色过滤管理员列表
    if current_user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN]:
        managers = await Admin.all()
    elif current_user.role == AdminRole.STORE_MANAGER:
        # 店长只能看到自己门店的员工和自己
        managers = await Admin.filter(
            store_id=current_user.store_id, role__in=[AdminRole.STORE_MANAGER, AdminRole.STAFF]
        )
    else:
        # 员工和运营只能看到自己
        managers = [current_user]

    return render_html('manager/list.html', {'managers': managers, 'current_user': current_user})


@router.get('/manager/form/')
@require_admin_manage()
async def manager_form(request: Request, manager_id: int | None = None) -> Response:
    """管理员用户创建和编辑页"""
    current_user = request.user

    # 获取可用的门店列表
    store_ids = await filter_stores_by_permission(current_user)
    stores = await ExhiHall.filter(id__in=store_ids) if store_ids else []

    # 获取可创建的角色列表
    available_roles = [role for role in AdminRole if current_user.can_create_role(role)]

    manager = None
    is_edit = False

    if manager_id:
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查是否有权限编辑此管理员
        if current_user.role == AdminRole.STORE_MANAGER:
            if manager.store_id != current_user.store_id:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        is_edit = True

    context = {
        'manager': manager,
        'is_edit': is_edit,
        'stores': stores,
        'available_roles': available_roles,
        'current_user': current_user,
        'AdminRole': AdminRole,
    }

    return render_html('manager/form.html', context)


@router.post('/manager/save/')
@require_admin_manage()
async def manager_save(
    request: Request,
    form: sch.AdminForm = Depends(sch.AdminForm.load),
    manager_id: int | None = Form(None),
    role: AdminRole = Form(...),
    store_id: int | None = Form(None),
) -> Response:
    """处理管理员用户创建和编辑表单"""
    current_user = request.user

    # 检查是否有权限创建/编辑此角色
    if not current_user.can_create_role(role):
        raise HTTPException(status_code=403, detail='无权限创建此角色的账号')

    # 检查门店权限
    if role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
        if not store_id:
            raise HTTPException(status_code=400, detail='店长和员工必须指定门店')

        # 检查是否有权限管理此门店
        if not check_store_permission(current_user, store_id):
            raise HTTPException(status_code=403, detail='无权限管理此门店')

    # 准备更新数据
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    update_data['role'] = role
    update_data['store_id'] = store_id

    if form.password:
        update_data['password'] = Admin.hash_password(form.password)

    if manager_id:
        # 编辑现有管理员
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查权限
        if current_user.role == AdminRole.STORE_MANAGER:
            if manager.store_id != current_user.store_id:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        await Admin.filter(id=manager_id).update(**update_data)
        await Admin.clear_cache(manager_id)
    else:
        # 创建新管理员
        await Admin.create(**update_data)

    return Redirect(url='/adm/manager/', status_code=303)


@router.get('/manager/delete/')
@require_admin_manage()
async def manager_delete(request: Request, manager_id: int) -> Response:
    """删除指定管理员用户"""
    current_user = request.user

    manager = await Admin.get_or_none(id=manager_id)
    if not manager:
        raise HTTPException(status_code=404, detail='管理员不存在')

    # 不能删除自己
    if manager.id == current_user.id:
        raise HTTPException(status_code=400, detail='不能删除自己')

    # 检查权限
    if current_user.role == AdminRole.STORE_MANAGER:
        if manager.store_id != current_user.store_id or manager.role != AdminRole.STAFF:
            raise HTTPException(status_code=403, detail='店长只能删除自己门店的员工')
    elif current_user.role == AdminRole.ADMIN:
        if manager.role == AdminRole.SUPER_ADMIN:
            raise HTTPException(status_code=403, detail='普通管理员不能删除超级管理员')

    await manager.delete()
    return Redirect(url='/adm/manager/', status_code=303)
