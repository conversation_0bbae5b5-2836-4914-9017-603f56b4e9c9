from fastapi import Depends, Form, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin

__all__ = ['manager_delete', 'manager_form', 'manager_list', 'manager_save']


@router.get('/manager/')
async def manager_list() -> Response:
    """管理员用户列表页"""
    managers = await Admin.all()
    return render_html('manager/list.html', {'managers': managers})


@router.get('/manager/form/')
async def manager_form(manager_id: int | None = None) -> Response:
    """管理员用户创建和编辑页"""
    manager, is_edit = (await Admin.get_or_none(id=manager_id), True) if manager_id else (None, False)
    return render_html('manager/form.html', {'manager': manager, 'is_edit': is_edit})


@router.post('/manager/save/')
async def manager_save(
    form: sch.AdminForm = Depends(sch.AdminForm.load),
    manager_id: int | None = Form(None),
) -> Response:
    """处理管理员用户创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    if form.password:
        update_data['password'] = Admin.hash_password(form.password)

    if manager_id:
        await Admin.filter(id=manager_id).update(**update_data)
        await Admin.clear_cache(manager_id)
    else:
        await Admin.create(**update_data)

    return Redirect(url='/adm/manager/', status_code=303)


@router.get('/manager/delete/')
async def manager_delete(manager_id: int) -> Response:
    """删除指定管理员用户"""
    await (await Admin.get(id=manager_id)).delete()
    return Redirect(url='/adm/manager/', status_code=303)
