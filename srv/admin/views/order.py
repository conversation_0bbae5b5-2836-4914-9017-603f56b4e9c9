from fastapi import Depends, Form, Query, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from apps.exhi.models import Ticket
from apps.order.models import Order, OrderStatus
from apps.user.models import User
from config import DEBUG

if DEBUG:
    __all__ = ['order_delete', 'order_form', 'order_list', 'order_save']
else:
    __all__ = ['order_list']


@router.get('/order/')
async def order_list() -> Response:
    """订单列表页，按创建时间倒序"""
    orders = await Order.all()
    # N+1 问题, 后续可以优化
    for order in orders:
        await order.prefetch()
    return render_html('order/list.html', {'orders': orders})


@router.get('/order/form/')
async def order_form(order_id: int | None = Query(None)) -> Response:
    """订单创建和编辑页"""
    order, is_edit = (await Order.get(id=order_id), True) if order_id else (None, False)
    users = await User.all()
    from apps.exhi.schemas import TicketCategory

    context = {
        'order': order,
        'statuses': OrderStatus,
        'categories': TicketCategory,
        'users': users,
        'is_edit': is_edit,
    }
    return render_html('order/form.html', context)


@router.post('/order/save/')
async def order_save(
    form: sch.OrderForm = Depends(sch.OrderForm.load),
    order_id: int | None = Form(None),
) -> Response:
    """处理订单创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    if order_id:
        await Order.filter(id=order_id).update(**update_data)
        await Order.clear_cache(order_id)
    else:
        tk = await Ticket.get(id=form.tid)
        update_data['archive'] = await tk.detail()
        await Order.create(**update_data)

    return Redirect(url='/adm/order/', status_code=303)


@router.get('/order/delete/')
async def order_delete(order_id: int) -> Response:
    """删除指定订单"""
    await (await Order.get(id=order_id)).delete()
    return Redirect(url='/adm/order/', status_code=303)
