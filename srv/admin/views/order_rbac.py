from fastapi import Depends, Form, Query, Request, Response, HTTPException
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin, AdminRole
from admin.permissions import require_permission, Permissions, filter_stores_by_permission
from apps.exhi.models import Ticket
from apps.order.models import Order, OrderStatus
from apps.user.models import User
from config import DEBUG

if DEBUG:
    __all__ = ['order_delete', 'order_form', 'order_list', 'order_save']
else:
    __all__ = ['order_list']


@router.get('/order/')
@require_permission(Permissions.ORDER_VIEW)
async def order_list(request: Request) -> Response:
    """订单列表页，按创建时间倒序"""
    current_user = request.user
    
    # 根据用户角色过滤订单
    if current_user.role in [AdminRole.SUPER_ADMIN, AdminRole.ADMIN, AdminRole.OPERATOR]:
        # 超级管理员、普通管理员和运营可以查看所有订单
        orders = await Order.all()
    elif current_user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
        # 店长和员工只能查看自己门店的订单
        if current_user.store_id:
            orders = await Order.filter(hid=current_user.store_id)
        else:
            orders = []
    else:
        orders = []
    
    # N+1 问题, 后续可以优化
    for order in orders:
        await order.prefetch()
    
    return render_html('order/list.html', {'orders': orders, 'current_user': current_user})


@router.get('/order/form/')
@require_permission(Permissions.ORDER_CREATE)
async def order_form(request: Request, order_id: int | None = Query(None)) -> Response:
    """订单创建和编辑页"""
    current_user = request.user
    
    order = None
    is_edit = False
    
    if order_id:
        order = await Order.get_or_none(id=order_id)
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")
        
        # 检查是否有权限编辑此订单
        if current_user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
            if order.hid != current_user.store_id:
                raise HTTPException(status_code=403, detail="无权限编辑此订单")
        
        is_edit = True
    
    # 获取用户列表
    users = await User.all()
    
    # 获取可用的门店列表
    store_ids = await filter_stores_by_permission(current_user)
    
    from apps.exhi.schemas import TicketCategory
    
    context = {
        'order': order,
        'statuses': OrderStatus,
        'categories': TicketCategory,
        'users': users,
        'is_edit': is_edit,
        'current_user': current_user,
        'allowed_store_ids': store_ids,
    }
    return render_html('order/form.html', context)


@router.post('/order/save/')
@require_permission(Permissions.ORDER_CREATE)
async def order_save(
    request: Request,
    form: sch.OrderForm = Depends(sch.OrderForm.load),
    order_id: int | None = Form(None),
) -> Response:
    """处理订单创建和编辑表单"""
    current_user = request.user
    
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    
    # 检查门店权限
    if 'hid' in update_data:
        if not current_user.can_manage_store(update_data['hid']):
            raise HTTPException(status_code=403, detail="无权限管理此门店的订单")
    
    if order_id:
        # 编辑现有订单
        order = await Order.get_or_none(id=order_id)
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")
        
        # 检查权限
        if current_user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
            if order.hid != current_user.store_id:
                raise HTTPException(status_code=403, detail="无权限编辑此订单")
        
        await Order.filter(id=order_id).update(**update_data)
        await Order.clear_cache(order_id)
    else:
        # 创建新订单
        tk = await Ticket.get(id=form.tid)
        
        # 检查门票所属门店权限
        if not current_user.can_manage_store(tk.hid):
            raise HTTPException(status_code=403, detail="无权限为此门店创建订单")
        
        update_data['archive'] = await tk.detail()
        await Order.create(**update_data)
    
    return Redirect(url='/adm/order/', status_code=303)


@router.get('/order/delete/')
@require_permission(Permissions.ORDER_DELETE)
async def order_delete(request: Request, order_id: int) -> Response:
    """删除指定订单"""
    current_user = request.user
    
    order = await Order.get_or_none(id=order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 检查权限
    if current_user.role in [AdminRole.STORE_MANAGER, AdminRole.STAFF]:
        if order.hid != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权限删除此订单")
    
    # 只有超级管理员可以删除订单
    if current_user.role != AdminRole.SUPER_ADMIN:
        raise HTTPException(status_code=403, detail="只有超级管理员可以删除订单")
    
    await order.delete()
    return Redirect(url='/adm/order/', status_code=303)
