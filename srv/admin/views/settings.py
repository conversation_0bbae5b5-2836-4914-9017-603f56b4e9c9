import json
import os
from typing import Any

from fastapi import File, Form, Response, UploadFile

from admin.http import abort, render_html, render_json, router
from admin.models import Setting
from apps.exhi.models import Ticket
from config import BRAND_BG, LOGO, MAX_SIZE, SETTINGS, SLIDES, UPLOAD_DIR, UPLOAD_URL
from libs.utils import file_size, file_type, save_upfile

__all__ = ['save_setting', 'settings_list', 'upload_bg', 'upload_logo', 'upload_slide']


@router.get('/settings/')
async def settings_list() -> Response:
    """系统设置页"""
    settings = await Setting.values(*SETTINGS.keys())

    # 检查轮播图是否存在
    slide_paths: Any = []
    for filename in SLIDES:
        slide_path = f'{UPLOAD_DIR}/{filename}'
        if os.path.exists(slide_path):
            slide_paths.append(f'{UPLOAD_URL}/{filename}')
        else:
            slide_paths.append(None)
    settings.set('slides', slide_paths)

    # 检查企业 Logo 和背景图是否存在
    settings.set('logo', f'{UPLOAD_URL}/{LOGO}' if os.path.exists(f'{UPLOAD_DIR}/{LOGO}') else None)
    settings.set('brand_bg', f'{UPLOAD_URL}/{BRAND_BG}' if os.path.exists(f'{UPLOAD_DIR}/{BRAND_BG}') else None)

    tickets = await Ticket.in_times()
    return render_html(
        'settings/list.html',
        {'settings': settings, 'tickets': tickets, 'UPLOAD_URL': UPLOAD_URL, 'LOGO': LOGO, 'BRAND_BG': BRAND_BG},
    )


@router.post('/settings/save/')
async def save_setting(name: str = Form(...), value: str = Form(...), vtype: str = Form(...)) -> Response:
    """保存设置项"""
    try:
        # 获取或创建设置项
        setting, _ = await Setting.get_or_create(name=name, defaults={'vtype': vtype})

        # 根据类型保存值
        if vtype == 'bool':
            setting.vint = 1 if value.lower() in ('true', '1', 'on') else 0
        elif vtype == 'json':
            setting.vjson = json.loads(value)
        elif vtype == 'text':
            setting.vtext = value.replace('\r\n', '\n')
        elif vtype == 'str':
            setting.vstr = value
        elif vtype == 'int':
            setting.vint = int(value)
        elif vtype == 'float':
            setting.vfloat = float(value)

        await setting.save()
        return render_json({'msg': '保存成功'})

    except Exception as e:
        return abort(400, f'保存失败: {e!s}')


@router.post('/settings/upload-slide/')
async def upload_slide(slide_num: int = Form(...), file: UploadFile = File(...)) -> Response:
    """上传轮播图"""
    try:
        # 验证文件类型
        if file_type(file) != 'jpg':
            return abort(400, '只支持 JPG 格式的图片')

        # 验证文件大小
        if file_size(file) > MAX_SIZE:
            return abort(400, f'文件大小不能超过 {MAX_SIZE // (1024 * 1024)}MB')

        # 确保上传目录存在
        os.makedirs(UPLOAD_DIR, exist_ok=True)

        # 保存文件
        file_url = await save_upfile(file, filename=SLIDES[slide_num])

        return render_json({'url': file_url, 'msg': '上传成功'})

    except Exception as e:
        return abort(400, f'上传失败: {e!s}')


@router.post('/settings/upload-logo/')
async def upload_logo(file: UploadFile = File(...)) -> Response:
    """上传品牌 Logo"""
    try:
        # 验证文件类型
        if file_type(file) != 'png':
            return abort(400, '只支持 PNG 格式的图片')

        # 验证文件大小
        if file_size(file) > MAX_SIZE:
            return abort(400, f'文件大小不能超过 {MAX_SIZE // (1024 * 1024)}MB')

        # 确保上传目录存在
        os.makedirs(UPLOAD_DIR, exist_ok=True)

        # 保存文件
        file_url = await save_upfile(file, filename=LOGO)

        return render_json({'url': file_url, 'msg': '上传成功'})

    except Exception as e:
        return abort(400, f'上传失败: {e!s}')


@router.post('/settings/upload-bg/')
async def upload_bg(file: UploadFile = File(...)) -> Response:
    """上传品牌背景图"""
    try:
        # 验证文件类型
        if file_type(file) != 'jpg':
            return abort(400, '只支持 JPG 格式的图片')

        # 验证文件大小
        if file_size(file) > MAX_SIZE:
            return abort(400, f'文件大小不能超过 {MAX_SIZE // (1024 * 1024)}MB')

        # 确保上传目录存在
        os.makedirs(UPLOAD_DIR, exist_ok=True)

        # 保存文件
        file_url = await save_upfile(file, filename=BRAND_BG)

        return render_json({'url': file_url, 'msg': '上传成功'})

    except Exception as e:
        return abort(400, f'上传失败: {e!s}')
