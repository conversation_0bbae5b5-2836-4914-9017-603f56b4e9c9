import math

from fastapi import Depends, Form, Query, Request, Response, HTTPException
from fastapi.responses import RedirectResponse as Redirect
from tortoise.expressions import Q

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin, AdminRole
from admin.permissions import require_permission, Permissions
from apps.user.models import User
from config import DEBUG, PAGE_SIZE

if DEBUG:
    __all__ = ['user_delete', 'user_form', 'user_list', 'user_save']
else:
    __all__ = ['user_list']


@router.get('/user/')
@require_permission(Permissions.USER_VIEW)
async def user_list(request: Request, page: int = Query(1, ge=1), q: str = Query(None)) -> Response:
    """用户列表页，支持分页和模糊搜索"""
    current_user = request.user
    
    query = User.all()
    if q:
        query = query.filter(Q(name__icontains=q) | Q(phone__icontains=q))

    # 根据角色过滤用户数据
    # 注意：用户表没有直接的门店关联，这里可能需要通过订单关联来过滤
    # 为了简化，这里暂时不做门店级别的过滤，所有有权限的角色都能看到所有用户
    # 实际项目中可能需要根据业务需求调整

    total_count = await query.count()
    total_pages = math.ceil(total_count / PAGE_SIZE)
    users = await query.offset((page - 1) * PAGE_SIZE).limit(PAGE_SIZE)

    context = {
        'users': users,
        'page': page,
        'total_pages': total_pages,
        'q': q,
        'current_user': current_user,
    }
    return render_html('user/list.html', context)


@router.get('/user/form/')
@require_permission(Permissions.USER_CREATE)
async def user_form(request: Request, user_id: int | None = Query(None)) -> Response:
    """用户创建和编辑页"""
    current_user = request.user
    
    user = None
    is_edit = False
    
    if user_id:
        user = await User.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        is_edit = True
    
    context = {
        'user': user,
        'is_edit': is_edit,
        'current_user': current_user,
    }
    return render_html('user/form.html', context)


@router.post('/user/save/')
@require_permission(Permissions.USER_CREATE)
async def user_save(
    request: Request,
    form: sch.UserForm = Depends(sch.UserForm.load),
    user_id: int | None = Form(None),
) -> Response:
    """处理用户创建和编辑表单"""
    current_user = request.user
    
    update_data = form.model_dump()
    
    if user_id:
        # 编辑现有用户
        user = await User.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 检查是否有编辑权限
        if not current_user.has_permission(Permissions.USER_EDIT):
            raise HTTPException(status_code=403, detail="无权限编辑用户")
        
        await User.filter(id=user_id).update(**update_data)
        await User.clear_cache(user_id)
    else:
        # 创建新用户
        await User.create(**update_data)

    return Redirect(url='/adm/user/', status_code=303)


if DEBUG:
    @router.get('/user/delete/')
    @require_permission(Permissions.USER_DELETE)
    async def user_delete(request: Request, user_id: int) -> Response:
        """删除指定用户"""
        current_user = request.user
        
        user = await User.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 只有超级管理员可以删除用户
        if current_user.role != AdminRole.SUPER_ADMIN:
            raise HTTPException(status_code=403, detail="只有超级管理员可以删除用户")
        
        await user.delete()
        return Redirect(url='/adm/user/', status_code=303)
