import datetime
import logging
import time
import uuid

from fastapi import APIRouter, Request
from tortoise.exceptions import DoesNotExist
from tortoise.expressions import Q

import config as cfg
from apps.exhi.models import Joint, Ticket
from apps.exhi.schemas import TicketCategory as Catg
from apps.order.models import Order, OrderStatus, Reservation
from apps.order.schemas import CheckinForm, OidForm, OrderSubmitForm
from libs.attrdict import AttrDict
from libs.http import abort
from libs.state import State
from libs.wxmp import wxpay

router = APIRouter()
logger = logging.getLogger('payment')


@router.post('/order/submit')
async def submit_order(form: OrderSubmitForm):
    """创建订单"""

    # 检查展票是否存在
    tk_cls = Ticket if form.catg in [Catg.ticket, Catg.reserve] else Joint
    if tk := await tk_cls.get(id=form.tid):
        hall_id = tk.hid
    else:
        return abort(404, '展票 ID 错误，请刷新后重试')

    # 检查展票价格
    unit_price = tk.price_of(form.timeslot, form.grade)
    if unit_price <= 0 or unit_price * form.quantity != form.amount:
        return abort(410, '价格或有变动，请刷新后重试')

    try:
        # 创建订单
        user = State.get('user')
        order = await Order.create(
            uid=user.id,
            tid=form.tid,
            hid=hall_id,
            phone=user.phone,
            catg=form.catg,
            quantity=form.quantity,
            amount=form.amount,
            timeslot=form.timeslot,
            grade=form.grade,
            archive=await tk.detail(),
        )
    except Exception as e:
        logger.error(f'创建订单失败: {e}')
        return abort(500, f'创建订单失败: {e}')

    try:
        # 调用微信支付
        await order.prefetch()
        code, message = wxpay.pay(
            description=await order.description(),
            out_trade_no=order.trade_no,
            amount={'total': int(form.amount * 100), 'currency': 'CNY'},
            payer={'openid': user.openid},
            time_expire=order.expire_time.isoformat(),
            notify_url=cfg.WX_PAYMENT.notify_url,
            sub_appid=order.exhihall.appid,  # 子商户应用ID (服务商模式)
            sub_mchid=order.exhihall.mchid,  # 子商户的商户号 (服务商模式)
            pay_type=cfg.WX_PAYMENT.wechatpay_type,
        )
    except Exception as e:
        logger.error(f'发起支付失败: {e}')
        return abort(500, f'发起支付失败: {e}')

    result = AttrDict(message)
    if code == 200:
        # 保存 prepay_id
        order.wx_prepay_id = result.prepay_id
        await order.save()
        # 返回支付参数
        signature_fields = {
            'appId': cfg.WX_PAYMENT.appid,
            'timeStamp': str(int(time.time())),
            'nonceStr': uuid.uuid4().hex,
            'package': f'prepay_id={result.prepay_id}',
            'signType': 'RSA',
        }
        signature_fields['paySign'] = wxpay.sign(signature_fields)
        return signature_fields
    else:
        return abort(result.code or code or 400)


@router.get('/order/pay')
async def pay_order(order_id: int):
    """重新发起支付 (用户提交订单时未支付，后在订单处重新发起支付时使用)"""
    order = await Order.get(id=order_id)
    # 返回支付参数
    signature_fields = {
        'appId': cfg.WX_PAYMENT.appid,
        'timeStamp': str(int(time.time())),
        'nonceStr': uuid.uuid4().hex,
        'package': f'prepay_id={order.wx_prepay_id}',
        'signType': 'RSA',
    }
    signature_fields['paySign'] = wxpay.sign(signature_fields)
    return signature_fields


@router.post('/payment/callback')
async def payment_callback(request: Request):
    """支付回调处理"""
    # 解析回调数据
    headers = dict(request.headers)
    body = await request.body()
    body_str = body.decode('utf-8')
    notified = AttrDict(wxpay.callback(headers, body_str))

    # 获取订单信息
    if not notified.out_trade_no:
        logger.error('回调数据中缺少 out_trade_no')
        return abort(400, '缺少订单号')

    try:
        # 查找订单
        order = await Order.get(trade_no=notified.out_trade_no)
        if notified.trade_state == 'SUCCESS':
            if notified.transaction_id:
                await order.pay_success(notified.transaction_id)  # 支付成功
                logger.info(f'订单支付成功: {notified.out_trade_no}')
            else:
                logger.error('回调数据中缺少 transaction_id')
        else:
            await order.pay_failed()  # 支付失败
            logger.error(f'订单支付失败: {notified.out_trade_no}, 状态: {notified.trade_state}')

        return {'code': 'SUCCESS', 'msg': '处理成功'}

    except DoesNotExist:
        logger.error(f'订单不存在: {notified.out_trade_no}')
        return abort(400, f'订单 "{notified.out_trade_no}" 不存在')

    except Exception as e:
        logger.error(f'支付回调处理失败: {e}')
        return abort(500, f'处理失败: {e}')


@router.post('/order/checkin')
async def order_checkin(form: CheckinForm):
    """检查订单状态"""
    user = State.get('user')
    if not user.is_adm:
        return abort(403, '没有核销权限')

    try:
        order = await Order.get(vcode=form.vcode)
        await order.checkin()
        return {'msg': '核销成功', 'order': await order.detail()}
    except DoesNotExist:
        return abort(404, '验证码错误')
    except Exception as e:
        return abort(500, f'核销失败: {e}')


@router.get('/order/list')
async def get_order_list(is_joint: bool = False, status: OrderStatus | None = None, page: int = 1):
    """获取用户订单列表"""
    try:
        # 构建查询条件
        user = State.get('user')
        condition = Q(uid=user.id)
        if status == OrderStatus.filter_refund:
            condition &= Q(status__in=[OrderStatus.refunding, OrderStatus.refunded])
        elif status:
            condition &= Q(status=status)
        query = Order.filter(condition)
        query = query.filter(catg=Catg.joint) if is_joint else query.exclude(catg=Catg.joint)

        # 分页查询
        offset = (page - 1) * cfg.PAGE_SIZE
        orders = await query.order_by('-created').limit(cfg.PAGE_SIZE).offset(offset)
        return [await order.detail() for order in orders]

    except Exception as e:
        return abort(500, f'获取订单列表失败: {e}')


@router.get('/order/detail')
async def get_order_detail(order_id: int):
    """获取订单详情"""
    if order := await Order.get_or_none(id=order_id):
        return await order.detail()  # 获取展览信息
    else:
        return abort(404, '订单不存在')


@router.post('/order/cancel')
async def cancel_order(form: OidForm):
    """取消订单"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
        await order.cancel()
        await order.prefetch('exhihall')
        code, message = wxpay.close(
            out_trade_no=order.trade_no,
            mchid=cfg.WX_PAYMENT.mchid,
            sub_mchid=order.exhihall.mchid,
        )
        if code == 200:
            return {'msg': '订单已取消'}
        else:
            return abort(code, message.get('errmsg') or '取消订单失败')
    except DoesNotExist:
        return abort(404, '订单不存在')
    except Exception as e:
        logger.error(f'取消订单失败: {e}')
        return abort(500, f'取消订单失败: {e}')


@router.post('/order/refund')
async def refund_order(form: OidForm):
    """申请退款"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
    except DoesNotExist:
        return abort(404, '订单不存在')

    try:
        # 修改订单状态，开始退款流程
        await order.start_refund()

        # 调用微信退款接口
        code, result = await wxpay.refund(
            out_refund_no=f'RF-{order.trade_no}',
            out_trade_no=order.trade_no,
            amount={'refund': int(order.amount * 100), 'total': int(order.amount * 100)},  # 转换为分,
            transaction_id=order.wx_transaction_id,
            reason=order.refund_reason,
            funds_account='AVAILABLE',
            notify_url=cfg.REFUND_NOTIFY_URL,
            sub_mchid=order.exhihall.mchid,
        )
        result = AttrDict(result)
        if code == 200:
            await order.refund_success(result.refund_id)  # 退款成功 # type: ignore
            return {'msg': '退款申请已提交'}
        else:
            return abort(code, result.errmsg or '退款失败')
    except Exception as e:
        order.status = OrderStatus.paid  # 恢复订单状态
        await order.save()
        logger.error(f'申请退款失败: {e}')
        return abort(500, f'申请退款失败: {e}')


@router.get('/refund/callback')
async def refund_callback(request: Request):
    """退款回调"""
    # 解析回调数据
    headers = dict(request.headers)
    body = await request.body()
    body_str = body.decode('utf-8')
    result = AttrDict(wxpay.callback(headers, body_str))

    if result.refund_status == 'SUCCESS':
        if order := await Order.get_or_none(trade_no=result.out_trade_no):
            await order.refund_success(result.refund_id)
            logger.info(f'退款回调成功: {result.out_trade_no}')
            return {'msg': '退款处理成功'}
        else:
            logger.error(f'退款回调失败: {result.out_trade_no} 订单不存在')
            return abort(500, '订单不存在')
    else:
        logger.error(f'退款回调失败: {result}')
        return abort(400, '退款回调失败')


@router.delete('/order/delete')
async def delete_order(form: OidForm):
    """删除订单"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
        await order.delete()
    except DoesNotExist:
        return abort(404, '订单不存在')
    except Exception as err:
        return abort(500, err)
    return {'msg': '订单已删除'}


@router.get('/reserve/list')
async def reserve_list():
    """预约列表"""
    tickets = await Ticket.filter(catg=Catg.reserve).order_by('-id')
    return {'tickets': [await exhi.detail() for exhi in tickets]}


@router.get('/reserve/detail')
async def reserve_detail():
    """预约详情"""
    return {}


@router.get('/reserve/prices')
async def reserve_prices(tid: int):
    """预约票价格"""
    if tk := await Ticket.get_or_none(id=tid):
        return {'title': tk.title, 'prices': tk.prices, 'catg': tk.catg}
    else:
        return {'error': '展览不存在'}


@router.post('/reserve/confirm')
async def reserve_confirm(tid: int, oid: int, date: str):
    """预约确认"""
    user = State.get('user')
    await Reservation.create(uid=user.id, tid=tid, oid=oid, time=datetime.date.fromisoformat(date))
    return {}


@router.get('/reserve/cancel')
async def reserve_cancel():
    """预约取消"""
    return {}
