from datetime import datetime
from enum import StrEnum

from pydantic import BaseModel, Field, field_validator

from apps.exhi.schemas import TicketCategory as Catg


class OrderStatus(StrEnum):
    pending = '待支付'
    failed = '支付失败'
    paid = '待使用'  # 支付成功，但未核销
    used = '已使用'
    canceled = '已取消'
    closed = '已关闭'
    refunding = '退款中'
    refunded = '已退款'
    filter_refund = '退款'  # 用于状态查询，表示所有退款状态


class OrderSubmitForm(BaseModel):
    """订单创建请求"""

    tid: int = Field(..., description='展票ID')
    catg: str = Field(..., description='票种类型')
    quantity: int = Field(..., ge=1, le=10, description='购买数量')
    amount: float = Field(..., gt=0, description='订单价格')
    timeslot: str = Field(..., description='场次')
    grade: str = Field(..., description='票档')

    @field_validator('catg')
    @classmethod
    def validate_ticket_catg(cls, v):
        if v not in Catg:
            raise ValueError('票种类型无效')
        return v


class PaymentResp(BaseModel):
    """支付响应"""

    order_id: int
    trade_no: str
    prepay_id: str
    pay_params: dict
    expire_time: datetime


class CheckinForm(BaseModel):
    """核销请求"""

    vcode: str = Field(..., max_length=64, description='订单ID')


class OidForm(BaseModel):
    order_id: int = Field(..., description='订单ID')
