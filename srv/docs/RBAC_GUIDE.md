# RBAC权限控制系统使用指南

## 概述

本系统实现了基于角色的访问控制（RBAC），为后台管理系统提供了细粒度的权限管理功能。

## 角色定义

### 1. 超级管理员（SUPER_ADMIN）
- **权限范围**：拥有所有系统权限
- **数据访问**：可以管理所有门店数据
- **账号创建**：可以创建任何级别的账号
- **特殊权限**：可以删除订单和用户

### 2. 普通管理员（ADMIN）
- **权限范围**：拥有除删除订单和用户外的所有权限
- **数据访问**：可以管理所有门店数据
- **账号创建**：可以创建店长、员工和运营账号
- **限制**：不能删除订单和用户，不能创建超级管理员

### 3. 店长（STORE_MANAGER）
- **权限范围**：只能查看和管理自己门店的数据
- **数据访问**：仅限自己关联的门店
- **账号创建**：可以创建本门店的员工账号
- **特殊功能**：可以验票
- **限制**：不能删除订单和用户

### 4. 员工（STAFF）
- **权限范围**：只能查看自己门店的数据
- **数据访问**：仅限自己关联的门店
- **特殊功能**：可以验票
- **限制**：无法创建账号，不能修改或删除数据

### 5. 运营账号（OPERATOR）
- **权限范围**：可以查看所有门店数据（只读权限）
- **数据访问**：所有门店（只读）
- **限制**：无法修改、删除任何数据，无法创建账号，无法验票

## 权限模块

### 用户管理权限
- `user.view` - 查看用户
- `user.create` - 创建用户
- `user.edit` - 编辑用户
- `user.delete` - 删除用户

### 订单管理权限
- `order.view` - 查看订单
- `order.create` - 创建订单
- `order.edit` - 编辑订单
- `order.delete` - 删除订单
- `order.refund` - 订单退款

### 展览管理权限
- `exhibition.view` - 查看展览
- `exhibition.create` - 创建展览
- `exhibition.edit` - 编辑展览
- `exhibition.delete` - 删除展览

### 展馆管理权限
- `exhihall.view` - 查看展馆
- `exhihall.create` - 创建展馆
- `exhihall.edit` - 编辑展馆
- `exhihall.delete` - 删除展馆

### 门票管理权限
- `ticket.view` - 查看门票
- `ticket.create` - 创建门票
- `ticket.edit` - 编辑门票
- `ticket.delete` - 删除门票
- `ticket.checkin` - 验票权限

### 管理员管理权限
- `admin.view` - 查看管理员
- `admin.create` - 创建管理员
- `admin.edit` - 编辑管理员
- `admin.delete` - 删除管理员

### 系统设置权限
- `setting.view` - 查看设置
- `setting.edit` - 编辑设置

## 使用方法

### 1. 在视图中使用权限装饰器

```python
from admin.permissions import require_permission, Permissions

@router.get('/user/')
@require_permission(Permissions.USER_VIEW)
async def user_list(request: Request) -> Response:
    # 视图逻辑
    pass
```

### 2. 使用角色装饰器

```python
from admin.permissions import require_role
from admin.models import AdminRole

@router.get('/admin-only/')
@require_role(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
async def admin_only_view(request: Request) -> Response:
    # 只有超级管理员和普通管理员可以访问
    pass
```

### 3. 检查门店权限

```python
from admin.permissions import check_store_access, filter_stores_by_permission

async def some_view(request: Request):
    current_user = request.user
    
    # 检查是否可以访问特定门店
    if not check_store_access(current_user, store_id):
        raise HTTPException(status_code=403, detail="无权限访问此门店")
    
    # 获取用户有权限的门店列表
    allowed_stores = await filter_stores_by_permission(current_user)
```

### 4. 在模板中检查权限

```html
<!-- 根据用户角色显示不同内容 -->
{% if current_user.role == 'super_admin' %}
    <button>删除</button>
{% endif %}

{% if current_user.has_permission('user.create') %}
    <a href="/adm/user/form/">创建用户</a>
{% endif %}
```

## 数据库迁移

### 运行初始化迁移

```bash
cd srv
python migrations/rbac_init.py
```

这将：
1. 创建权限表和角色权限关联表
2. 初始化所有权限数据
3. 为各角色分配默认权限
4. 升级现有管理员账号
5. 创建默认超级管理员（如果不存在）

### 默认管理员账号

- 用户名：`admin`
- 密码：`admin123`
- 角色：超级管理员

**重要：请在部署后立即修改默认密码！**

## 测试

运行权限系统测试：

```bash
cd srv
python -m pytest tests/test_rbac.py -v
```

## 注意事项

1. **门店关联**：店长和员工必须关联到具体的门店
2. **权限继承**：权限检查是基于角色的，不支持权限继承
3. **数据隔离**：店长和员工只能访问自己门店的数据
4. **账号创建**：严格按照角色层级进行账号创建权限控制
5. **验票权限**：只有店长、员工和管理员可以验票，运营账号不能验票

## 扩展

### 添加新权限

1. 在 `admin/permissions.py` 的 `Permissions` 类中添加新权限常量
2. 在 `DEFAULT_ROLE_PERMISSIONS` 中为相应角色分配权限
3. 运行迁移脚本更新数据库
4. 在视图中使用 `@require_permission` 装饰器

### 添加新角色

1. 在 `admin/models.py` 的 `AdminRole` 枚举中添加新角色
2. 在 `DEFAULT_ROLE_PERMISSIONS` 中定义角色权限
3. 更新 `can_create_role` 方法的层级关系
4. 运行迁移脚本

## 故障排除

### 常见问题

1. **权限不生效**：检查是否正确加载了用户权限（`await user.load_permissions()`）
2. **门店权限错误**：确认店长/员工账号正确关联了门店ID
3. **装饰器不工作**：确保视图函数的第一个参数是 `Request` 对象
4. **模板权限检查失败**：确保在模板上下文中传递了 `current_user`

### 调试技巧

```python
# 检查用户权限
await user.load_permissions()
print(f"用户权限: {user._permissions}")

# 检查角色
print(f"用户角色: {user.role}")

# 检查门店权限
print(f"可以管理门店 {store_id}: {user.can_manage_store(store_id)}")
```
