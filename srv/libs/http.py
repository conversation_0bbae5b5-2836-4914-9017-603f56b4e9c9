from http import HTTPStatus

from fastapi.responses import JSONResponse


def render(data: dict, status_code: int = 200) -> JSONResponse:
    """渲染 JSON 数据"""
    return JSONResponse(data, status_code=status_code)


def abort(status_code: int = 400, reason: str | Exception = '') -> JSONResponse:
    """渲染错误数据"""
    if reason:
        if isinstance(reason, Exception):
            reason = str(reason) or reason.__class__.__name__
        return JSONResponse({'detail': reason}, status_code=status_code)
    elif status_code in HTTPStatus:
        return JSONResponse({'detail': HTTPStatus(status_code).phrase}, status_code=status_code)
    else:
        return JSONResponse({'detail': '请求异常'}, status_code=status_code)
