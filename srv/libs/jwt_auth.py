import time
from typing import <PERSON>Var
from urllib.parse import urlparse

from fastapi import Request, Response
from fastapi.responses import RedirectResponse as Redirect
from jose import ExpiredSignatureError, JWTError, jwt
from starlette.authentication import UnauthenticatedUser
from tortoise.exceptions import DoesNotExist

from admin.models import Admin
from apps.user.models import User
from config import JWT_ALGORITHM, SECRET_KEY, SESSION_MAX_AGE
from libs.http import abort
from libs.middleware import BaseMiddleware
from libs.state import State


class JWTMiddleware(BaseMiddleware):
    """JWT 验证中间件"""

    def __init__(self, *args, **kwargs):
        self.payload = None
        super().__init__(*args, **kwargs)

    WHITE_LIST: ClassVar[list[str]] = [
        '/api/weixin/login',
        '/api/payment/callback',
        '/api/refund/callback',
        '/adm/login',
        '/static',
        '/docs',
        '/redoc',
        '/openapi.json',
    ]

    async def process_request(self, request: Request):
        """验证 Token"""
        path = request.url.path

        for white_path in self.WHITE_LIST:
            if path.startswith(white_path):
                request.scope['user'] = UnauthenticatedUser()
                return

        # 提取 JWT 令牌
        referrer = urlparse(request.headers.get('Referer', ''))
        if path.startswith('/adm') or referrer.path.startswith('/docs'):
            auth_header = request.cookies.get('Authorization', ' ')
        else:
            auth_header = request.headers.get('Authorization', ' ')

        try:
            # 验证 JWT 令牌
            token = auth_header.split(' ')[1]
            self.payload = jwt.decode(token, SECRET_KEY, algorithms=[JWT_ALGORITHM])
        except ExpiredSignatureError:
            return abort(401, 'Token 已过期, 请重新进入小程序')
        except JWTError:
            if path.startswith('/adm'):
                return Redirect('/adm/login')
            else:
                return abort(401, '无效的 Token')

        try:
            uid = self.payload['uid']
            user = await (Admin.get(id=uid) if self.payload.get('is_adm') is True else User.get(id=uid))
            request.scope['user'] = user  # 将用户信息存储到请求上下文中
            State.set(user=user)  # 将用户信息存储到请求上下文中
            return
        except (DoesNotExist, KeyError):
            return abort(410, '用户不存在')

    async def process_response(self, request: Request, response: Response):
        """检查 Token 过期时间，如果临近过期则刷新 Token"""
        if response.status_code == 200 and 'user' in request.scope:
            now = int(time.time())
            if isinstance(self.payload, dict) and (exp := self.payload.get('exp')):
                if (now + 3600) >= exp:
                    self.payload['iat'] = now
                    self.payload['exp'] = now + SESSION_MAX_AGE
                    response.headers['RefreshedToken'] = jwt.encode(self.payload, SECRET_KEY, algorithm=JWT_ALGORITHM)

        return response
