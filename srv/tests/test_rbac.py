"""
RBAC权限系统测试用例
"""
import pytest
from tortoise.contrib.test import TestCase

from admin.models import Admin, AdminRole, Permission, RolePermission
from admin.permissions import Permissions, DEFAULT_ROLE_PERMISSIONS
from apps.exhi.models import ExhiHall


class TestRBACSystem(TestCase):
    """RBAC权限系统测试"""
    
    async def setUp(self):
        """测试前准备"""
        # 创建测试门店
        self.store1 = await ExhiHall.create(
            name="测试门店1",
            city="北京",
            addr="北京市朝阳区测试地址1"
        )
        self.store2 = await ExhiHall.create(
            name="测试门店2", 
            city="上海",
            addr="上海市浦东新区测试地址2"
        )
        
        # 创建权限
        self.permissions = {}
        for perm_code in [Permissions.USER_VIEW, Permissions.ORDER_VIEW, Permissions.TICKET_CHECKIN]:
            perm = await Permission.create(
                code=perm_code,
                name=perm_code.replace('.', '_'),
                description=f"测试权限: {perm_code}",
                module=perm_code.split('.')[0]
            )
            self.permissions[perm_code] = perm
        
        # 为角色分配权限
        for role, perm_codes in DEFAULT_ROLE_PERMISSIONS.items():
            for perm_code in perm_codes:
                if perm_code in self.permissions:
                    await RolePermission.create(
                        role=role,
                        permission_id=self.permissions[perm_code].id
                    )
    
    async def test_super_admin_permissions(self):
        """测试超级管理员权限"""
        admin = await Admin.create(
            username="super_admin",
            password=Admin.hash_password("password123"),
            role=AdminRole.SUPER_ADMIN,
            is_active=True
        )
        
        await admin.load_permissions()
        
        # 超级管理员应该有所有权限
        assert admin.has_permission(Permissions.USER_VIEW)
        assert admin.has_permission(Permissions.ORDER_VIEW)
        assert admin.has_permission(Permissions.TICKET_CHECKIN)
        
        # 超级管理员可以管理所有门店
        assert admin.can_manage_store(self.store1.id)
        assert admin.can_manage_store(self.store2.id)
        
        # 超级管理员可以创建任何角色
        assert admin.can_create_role(AdminRole.ADMIN)
        assert admin.can_create_role(AdminRole.STORE_MANAGER)
        assert admin.can_create_role(AdminRole.STAFF)
    
    async def test_store_manager_permissions(self):
        """测试店长权限"""
        admin = await Admin.create(
            username="store_manager",
            password=Admin.hash_password("password123"),
            role=AdminRole.STORE_MANAGER,
            store_id=self.store1.id,
            is_active=True
        )
        
        await admin.load_permissions()
        
        # 店长应该有基本权限
        assert admin.has_permission(Permissions.USER_VIEW)
        assert admin.has_permission(Permissions.ORDER_VIEW)
        assert admin.has_permission(Permissions.TICKET_CHECKIN)
        
        # 店长只能管理自己的门店
        assert admin.can_manage_store(self.store1.id)
        assert not admin.can_manage_store(self.store2.id)
        
        # 店长只能创建员工
        assert not admin.can_create_role(AdminRole.ADMIN)
        assert not admin.can_create_role(AdminRole.STORE_MANAGER)
        assert admin.can_create_role(AdminRole.STAFF)
    
    async def test_staff_permissions(self):
        """测试员工权限"""
        admin = await Admin.create(
            username="staff",
            password=Admin.hash_password("password123"),
            role=AdminRole.STAFF,
            store_id=self.store1.id,
            is_active=True
        )
        
        await admin.load_permissions()
        
        # 员工应该有基本查看权限
        assert admin.has_permission(Permissions.USER_VIEW)
        assert admin.has_permission(Permissions.ORDER_VIEW)
        assert admin.has_permission(Permissions.TICKET_CHECKIN)
        
        # 员工只能管理自己的门店
        assert admin.can_manage_store(self.store1.id)
        assert not admin.can_manage_store(self.store2.id)
        
        # 员工不能创建任何角色
        assert not admin.can_create_role(AdminRole.ADMIN)
        assert not admin.can_create_role(AdminRole.STORE_MANAGER)
        assert not admin.can_create_role(AdminRole.STAFF)
    
    async def test_operator_permissions(self):
        """测试运营账号权限"""
        admin = await Admin.create(
            username="operator",
            password=Admin.hash_password("password123"),
            role=AdminRole.OPERATOR,
            is_active=True
        )
        
        await admin.load_permissions()
        
        # 运营账号只有查看权限
        assert admin.has_permission(Permissions.USER_VIEW)
        assert admin.has_permission(Permissions.ORDER_VIEW)
        assert not admin.has_permission(Permissions.TICKET_CHECKIN)
        
        # 运营账号可以查看所有门店数据
        assert admin.can_manage_store(self.store1.id)
        assert admin.can_manage_store(self.store2.id)
        
        # 运营账号不能创建任何角色
        assert not admin.can_create_role(AdminRole.ADMIN)
        assert not admin.can_create_role(AdminRole.STORE_MANAGER)
        assert not admin.can_create_role(AdminRole.STAFF)
    
    async def test_inactive_admin(self):
        """测试被禁用的管理员"""
        admin = await Admin.create(
            username="inactive_admin",
            password=Admin.hash_password("password123"),
            role=AdminRole.ADMIN,
            is_active=False  # 被禁用
        )
        
        # 被禁用的管理员不应该通过权限检查
        assert not admin.is_active
    
    async def test_role_hierarchy(self):
        """测试角色层级关系"""
        super_admin = await Admin.create(
            username="super_admin",
            password=Admin.hash_password("password123"),
            role=AdminRole.SUPER_ADMIN,
            is_active=True
        )
        
        admin = await Admin.create(
            username="admin",
            password=Admin.hash_password("password123"),
            role=AdminRole.ADMIN,
            is_active=True
        )
        
        store_manager = await Admin.create(
            username="store_manager",
            password=Admin.hash_password("password123"),
            role=AdminRole.STORE_MANAGER,
            store_id=self.store1.id,
            is_active=True
        )
        
        # 测试创建权限层级
        # 超级管理员可以创建所有角色
        assert super_admin.can_create_role(AdminRole.SUPER_ADMIN)
        assert super_admin.can_create_role(AdminRole.ADMIN)
        assert super_admin.can_create_role(AdminRole.STORE_MANAGER)
        assert super_admin.can_create_role(AdminRole.STAFF)
        
        # 普通管理员不能创建超级管理员
        assert not admin.can_create_role(AdminRole.SUPER_ADMIN)
        assert admin.can_create_role(AdminRole.STORE_MANAGER)
        assert admin.can_create_role(AdminRole.STAFF)
        
        # 店长只能创建员工
        assert not store_manager.can_create_role(AdminRole.ADMIN)
        assert not store_manager.can_create_role(AdminRole.STORE_MANAGER)
        assert store_manager.can_create_role(AdminRole.STAFF)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
